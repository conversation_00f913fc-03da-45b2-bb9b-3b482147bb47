import request from '../utils/request';

// 类型定义
export interface MembershipProduct {
    id: string;
    name: string;
    description: string;
    price: number;
    duration: number;
    sort: number;
    isActive: boolean;
    iconUrl?: string;
    permissions: Permission[];
}

export interface Permission {
    id: string;
    name: string;
    key: string;
    description: string;
}

export interface MembershipStatus {
    isMember: boolean;
    membershipType?: string;
    expiresAt?: string;
    daysRemaining?: number;
    permissions: string[];
}

export interface MembershipOrder {
    id: string;
    orderNo: string;
    productId: string;
    productName: string;
    price: number;
    amount: number;
    duration: number;
    status: 'pending' | 'paid' | 'cancelled' | 'refunded';
    paymentMethod?: string;
    transactionId?: string;
    clientId: string;
    createdAt: string;
    updatedAt: string;
    paidAt?: string;
    membershipProduct: MembershipProduct;
}

export interface CreateOrderRequest {
    productId: string;
}

export interface PayOrderRequest {
    orderNo: string;
    paymentMethod: 'alipay' | 'wxpay';
}

export interface PayOrderResponse {
    qrcode: string;
    payurl: string;
    img: string;
    orderNo: string;
    money: number;
    name: string;
}

export interface CheckPermissionRequest {
    permissionKey: string;
}

export interface CheckPermissionResponse {
    hasPermission: boolean;
}

export interface OrderListQuery {
    page?: number;
    pageSize?: number;
    status?: string;
}

export interface OrderListResponse {
    items: MembershipOrder[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
}

// API 方法

/**
 * 获取会员商品列表
 */
export function getMembershipProductsAPI() {
    return request<MembershipProduct[]>({
        url: '/client/membership/products',
        method: 'GET'
    });
}

/**
 * 获取当前用户会员状态
 */
export function getMembershipStatusAPI() {
    return request<MembershipStatus>({
        url: '/client/membership/status',
        method: 'GET'
    });
}

/**
 * 创建会员订单
 */
export function createMembershipOrderAPI(data: CreateOrderRequest) {
    return request<MembershipOrder>({
        url: '/client/membership/orders',
        method: 'POST',
        data
    });
}

/**
 * 获取订单列表
 */
export function getMembershipOrdersAPI(params?: OrderListQuery) {
    return request<OrderListResponse>({
        url: '/client/membership/orders',
        method: 'GET',
        params
    });
}

/**
 * 获取订单详情
 */
export function getMembershipOrderDetailAPI(orderNo: string) {
    return request<MembershipOrder>({
        url: '/client/membership/orders/' + orderNo,
        method: 'GET'
    });
}

/**
 * 创建支付二维码
 */
export function payMembershipOrderAPI(data: PayOrderRequest) {
    return request<PayOrderResponse>({
        url: '/client/membership/orders/pay',
        method: 'POST',
        data
    });
}

/**
 * 检查用户权限
 */
export function checkMembershipPermissionAPI(data: CheckPermissionRequest) {
    return request<CheckPermissionResponse>({
        url: '/client/membership/check-permission',
        method: 'POST',
        data
    });
}