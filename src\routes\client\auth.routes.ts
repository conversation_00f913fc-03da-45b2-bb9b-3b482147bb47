import Router from 'koa-router';
import { ClientAuthController } from '../../controllers/client/auth.controller';
import { clientAuthMiddleware } from '../../middleware/auth.middleware';

const router = new Router({ prefix: '/api/client/auth' });
const clientAuthController = new ClientAuthController();

// 公开路由
router.get('/captcha', clientAuthController.getCaptcha);
router.post('/send-register-code', clientAuthController.sendRegisterCode);
router.post('/register', clientAuthController.register);
router.post('/login', clientAuthController.login);
router.post('/forgot-password', clientAuthController.forgotPassword);
router.post('/reset-password', clientAuthController.resetPassword);

// 需要认证的路由
router.get('/profile', clientAuthMiddleware, clientAuthController.getProfile);
router.put('/profile', clientAuthMiddleware, clientAuthController.updateProfile);
router.put('/password', clientAuthMiddleware, clientAuthController.changePassword);

export default router;
