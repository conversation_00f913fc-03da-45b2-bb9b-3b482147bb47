import request from '../utils/request';

interface AppUserLoginParams {
    email: string
    password: string
    captchaAnswer?: string
    captchaId?: string
}
// 用户登录
export function loginAPI(data: AppUserLoginParams) {
    return request({
        url: '/client/auth/login',
        method: 'POST',
        data,
    })
}

interface AppUserRegisterParams {
    email: string,
    password: string,
    verificationCode?: string,
}
// 用户注册
export function registerAPI(data: AppUserRegisterParams) {
    return request({
        url: '/client/auth/register',
        method: 'POST',
        data,
    })
}

interface AppCaptchaParams {
    type?: string
    width?: string
    height?: string
}
// 获取图片验证码
export function getCaptchaAPI(params?: AppCaptchaParams) {
    return request({
        url: '/client/auth/captcha',
        method: 'GET',
        params,
    })
}

interface AppSendEmailCodeParams {
    email: string
}
// 发送邮箱验证码
export function sendEmailCodeAPI(data: AppSendEmailCodeParams) {
    return request({
        url: '/client/auth/send-register-code',
        method: 'POST',
        data,
    })
}
