import { ipc<PERSON><PERSON><PERSON> } from 'electron'
import { domReady, onLocalStorageItemChange } from './tools';
import { CommonBridge } from './common'

// 定义Bridge接口
interface IBridge {
    getUserId(): string | null;
    getUserAvatar(): string | null;
    getUserNickName(): string | null;
    getUnreadCount(): number;
    getLoginState(): boolean;
    messageObserver: MutationObserver | null
}

// 实现Bridge类
export class TelegramBridge extends CommonBridge implements IBridge {
    constructor() {
        super();
        // 确保方法绑定到实例
        this.getUserId = this.getUserId.bind(this);
        this.getUserAvatar = this.getUserAvatar.bind(this);
        this.getUserNickName = this.getUserNickName.bind(this);
        this.getUnreadCount = this.getUnreadCount.bind(this);
        this.getLoginState = this.getLoginState.bind(this);
        this.listenLocalStorageItemChange();
        this.listenChatMessageChange();
        domReady().then(async () => {
            // const fingerprintConfig = await this.getFingerprintConfig();
            // this.setFingerprint(fingerprintConfig);
            console.log('DOM ready');
            ipcRenderer.send('account-login', { state: false });
            ipcRenderer.on('translate-config-changed', () => {
                this.setTranslateState();
            });
            this.setTranslateState();
            this.setupTranslateInput();
        });
    }

    messageObserver = null;

    private async setupTranslateInput() {
        this.initTranslateInput('#editable-message-text');
    }

    private listenLocalStorageItemChange() {
        const targetKey = 'account1';
        onLocalStorageItemChange(targetKey, (value: string) => {
            try {
                const data = JSON.parse(value);
                ipcRenderer.send('account-login', { state: !!data.userId })
                ipcRenderer.send('localStorage-changed');
            } catch (e) {
                console.log('send account-login error:', e);
            }
        })
    }

    private listenChatMessageChange() {
        this.messageObserver = this.setupMessageObserver('#MiddleColumn', () => {
            this.initTranslateChatItems('.message-content', '.content-inner');
        });
    }

    public getUserId(): string | null {
        try {
            const account = localStorage.getItem('account1');
            if (!account) return null;
            const data = JSON.parse(account);
            return data.userId || null;
        } catch {
            return null;
        }
    }

    public getUserAvatar(): string | null {
        try {
            const account = localStorage.getItem('account1');
            if (!account) return null;
            const data = JSON.parse(account);
            return data.avatarUri || null;
        } catch {
            return null;
        }
    }

    public getUserNickName(): string | null {
        try {
            const account = localStorage.getItem('account1');
            if (!account) return null;
            const data = JSON.parse(account);
            return `${data.firstName || ''} ${data.lastName || ''}`.trim() || null;
        } catch {
            return null;
        }
    }

    public getUnreadCount(): number {
        try {
            const doms = document.querySelectorAll('.ChatBadge.unread');
            return Array.from(doms).reduce((previousValue, currentValue: any) => {
                const text = currentValue.innerText;
                return previousValue + Number(text || 0);
            }, 0);
        } catch {
            return 0;
        }
    }

    public getLoginState(): boolean {
        try {
            const account = localStorage.getItem('account1');
            if (!account) return false;
            const data = JSON.parse(account);
            return !!data.userId;
        } catch {
            return false;
        }
    }
}


