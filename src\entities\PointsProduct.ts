import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

/**
 * 积分商品实体
 */
@Entity('points_products')
export class PointsProduct {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @CreateDateColumn({ type: 'datetime' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'datetime' })
  updatedAt: Date;

  @Column({ length: 100, comment: '商品名称' })
  name: string;

  @Column({ type: 'text', nullable: true, comment: '商品描述' })
  description?: string;

  @Column({ type: 'int', comment: '积分数量' })
  points: number;

  @Column({ type: 'int', comment: '价格（分）' })
  price: number;

  @Column({
    type: 'decimal',
    precision: 3,
    scale: 2,
    default: 1.0,
    comment: '折扣率 (0-1)',
  })
  discountRate: number;

  @Column({ type: 'int', default: 0, comment: '赠送积分' })
  bonusPoints: number;

  @Column({ default: true, comment: '是否启用' })
  isActive: boolean;

  @Column({ type: 'int', default: 0, comment: '排序权重' })
  sort: number;

  /**
   * 计算实际价格
   */
  get actualPrice(): number {
    return Math.floor(this.price * this.discountRate);
  }

  /**
   * 计算总积分（基础积分 + 赠送积分）
   */
  get totalPoints(): number {
    return this.points + this.bonusPoints;
  }
}
