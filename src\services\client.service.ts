import { Repository, <PERSON><PERSON><PERSON> } from 'typeorm';
import { Client } from '../entities/Client';
import { BaseService } from './base.service';
import { AppDataSource } from '../config/database';
import { hashPassword, comparePassword, generateClientToken } from '../utils/account-auth.util';
import { sendResetPasswordEmail } from '../utils/email.util';
import { getCurrentTenantId } from '../utils/tenant.util';
import crypto from 'crypto';
import { CLIENT_URL } from '../config/app';

export interface ClientLoginDto {
  email: string;
  password: string;
}

export interface ClientRegisterDto {
  name: string;
  email: string;
  password: string;
  phone?: string;
}

export class ClientService extends BaseService<Client> {
  private clientRepository: Repository<Client>;

  constructor() {
    const clientRepository = AppDataSource.getRepository(Client);
    super(clientRepository);
    this.clientRepository = clientRepository;
  }

  /**
   * 注册客户端用户
   */
  async register(registerDto: ClientRegisterDto): Promise<{ client: Client; token: string }> {
    const { email, password, name, phone } = registerDto;

    // 检查邮箱是否已注册
    const existingClient = await this.clientRepository.findOne({ where: { email } });
    if (existingClient) {
      throw new Error('邮箱已被注册');
    }

    // 哈希密码并创建用户
    const hashedPassword = await hashPassword(password);

    // 获取当前租户ID
    const tenantId = getCurrentTenantId();

    const client = await this.create({
      email,
      password: hashedPassword,
      name,
      phone,
      tenantId,
    });

    // 生成JWT令牌
    const token = generateClientToken(client);

    return { client, token };
  }

  /**
   * 客户端用户登录
   */
  async login(loginDto: ClientLoginDto): Promise<{ client: Client; token: string }> {
    const { email, password } = loginDto;

    // 查找用户
    const client = await this.clientRepository.findOne({
      where: { email },
      select: [
        'id',
        'name',
        'email',
        'password',
        'phone',
        'tenantId',
        'isActive',
        'createdAt',
        'updatedAt',
      ],
    });

    if (!client) {
      throw new Error('账号不存在');
    }

    // 验证密码
    const isPasswordValid = await comparePassword(password, client.password);
    if (!isPasswordValid) {
      throw new Error('密码错误');
    }

    // 检查账号是否被禁用
    if (!client.isActive) {
      throw new Error('该账号已被禁用');
    }

    // 生成JWT令牌
    const token = generateClientToken(client);

    return { client, token };
  }

  /**
   * 忘记密码
   */
  async forgotPassword(email: string): Promise<boolean> {
    const client = await this.clientRepository.findOne({ where: { email } });
    if (!client) {
      throw new Error('找不到该邮箱对应的账号');
    }

    // 生成重置密码令牌
    const resetPasswordToken = crypto.randomBytes(32).toString('hex');
    const resetPasswordExpires = new Date();
    resetPasswordExpires.setHours(resetPasswordExpires.getHours() + 1); // 令牌有效期1小时

    // 更新用户信息
    await this.clientRepository.update(client.id, {
      resetPasswordToken,
      resetPasswordExpires,
    });

    // 发送重置密码邮件
    try {
      await sendResetPasswordEmail(
        client.email,
        client.name,
        `${CLIENT_URL}/reset-password?token=${resetPasswordToken}`,
      );
      return true;
    } catch (error) {
      console.error('发送重置密码邮件失败:', error);
      throw new Error('发送重置密码邮件失败');
    }
  }

  /**
   * 重置密码
   */
  async resetPassword(token: string, password: string, confirmPassword: string): Promise<boolean> {
    // 确认密码是否匹配
    if (password !== confirmPassword) {
      throw new Error('密码与确认密码不匹配');
    }

    // 查找对应的用户
    const client = await this.clientRepository.findOne({
      where: {
        resetPasswordToken: token,
        resetPasswordExpires: MoreThan(new Date()),
      },
    });

    if (!client) {
      throw new Error('重置密码令牌无效或已过期');
    }

    // 哈希新密码
    const hashedPassword = await hashPassword(password);

    // 更新用户密码并清除重置令牌
    await this.clientRepository.update(client.id, {
      password: hashedPassword,
      resetPasswordToken: '',
      resetPasswordExpires: undefined,
    });

    return true;
  }

  /**
   * 修改密码
   */
  async changePassword(
    clientId: string,
    oldPassword: string,
    newPassword: string,
    confirmPassword: string,
  ): Promise<boolean> {
    // 确认新密码是否匹配
    if (newPassword !== confirmPassword) {
      throw new Error('新密码与确认密码不匹配');
    }

    // 查找用户
    const client = await this.clientRepository.findOne({
      where: { id: clientId },
      select: ['id', 'password'],
    });

    if (!client) {
      throw new Error('用户不存在');
    }

    // 验证旧密码
    const isPasswordValid = await comparePassword(oldPassword, client.password);
    if (!isPasswordValid) {
      throw new Error('旧密码不正确');
    }

    // 哈希新密码
    const hashedPassword = await hashPassword(newPassword);

    // 更新密码
    await this.clientRepository.update(clientId, {
      password: hashedPassword,
    });

    return true;
  }
}
