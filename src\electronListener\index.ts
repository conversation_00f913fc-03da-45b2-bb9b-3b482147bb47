import { IAccountInfo } from "@/types";
import { IEmitType } from "../constants/enum";
import { useChatStore } from "../stores/chat";

export function setupElectronListener() {
    const chatStore = useChatStore()

    window.ipcRenderer?.on(IEmitType.OnChatWindowStartLoading, (_e: any, chatId: string) => {
        chatStore.setChatWindowState(chatId, 'isLoading', true);
    })

    window.ipcRenderer?.on(IEmitType.OnChatWindowReadyed, (_e: any, chatId: string) => {
        chatStore.setChatWindowState(chatId, 'isLoading', false);
        chatStore.setChatWindowState(chatId, 'isLoaded', true);
    })

    window.ipcRenderer?.on(IEmitType.OnChatWindowLoadFail, (_e: any, chatId: string) => {
        chatStore.setChatWindowState(chatId, 'isLoading', false);
        chatStore.setChatWindowState(chatId, 'isLoadFail', true);
    })

    window.ipcRenderer?.on(IEmitType.OnChatWindowCreated, (_e: any, chatId: string) => {
        chatStore.setChatWindowState(chatId, 'isCreated', true);
    })

    window.ipcRenderer?.on(IEmitType.OnPlatformAccountChange, (_e: any, chatId: string, accountInfo: IAccountInfo) => {
        chatStore.setChatWindowAccountInfo(chatId, accountInfo);
    })
}