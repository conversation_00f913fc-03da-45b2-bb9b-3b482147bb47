# 文档翻译任务数据库记录功能实现

## 🎯 功能概述

为文档翻译功能添加了完整的数据库记录系统，用于跟踪和管理用户提交的文档翻译任务信息，包括任务状态、进度、结果等。

## ✅ 已实现的功能

### 1. 数据库实体设计

#### DocumentTranslationTask 实体 (`src/entities/DocumentTranslationTask.ts`)

**核心字段**:
- `externalTaskId`: 外部任务ID（百度翻译返回的任务ID）
- `accountId`: 用户账号ID
- `vendor`: 翻译供应商（默认：baidu）
- `originalFilename`: 原始文件名
- `fileFormat`: 文件格式
- `fileSize`: 文件大小（字节）
- `sourceLanguage`: 源语言
- `targetLanguage`: 目标语言

**配置字段**:
- `domain`: 垂直领域（可选）
- `outputFormats`: 输出格式列表（JSON）
- `filenamePrefix`: 文件名前缀（可选）

**状态字段**:
- `status`: 任务状态（枚举）
- `reason`: 状态说明
- `resultFiles`: 翻译结果文件信息（JSON）
- `characterCount`: 字符数量

**时间字段**:
- `startedAt`: 任务开始时间
- `completedAt`: 任务完成时间
- `expiredAt`: 任务过期时间
- `createdAt`: 创建时间
- `updatedAt`: 更新时间

**调试字段**:
- `errorMessage`: 错误信息
- `rawResponse`: 原始API响应数据（JSON）

#### 状态枚举 (DocumentTranslationStatus)
```typescript
export enum DocumentTranslationStatus {
  NOT_STARTED = 'NotStarted',
  RUNNING = 'Running',
  SUCCEEDED = 'Succeeded',
  FAILED = 'Failed',
  EXPIRED = 'Expired',
}
```

### 2. 数据库服务层

#### DocumentTranslationTaskService (`src/services/document-translation-task.service.ts`)

**核心方法**:
- `createTask()`: 创建文档翻译任务记录
- `getTaskByExternalId()`: 根据外部任务ID获取任务
- `getTaskByAccountAndExternalId()`: 根据用户ID和外部任务ID获取任务
- `updateTaskStatus()`: 更新任务状态
- `getTasksByAccount()`: 获取用户的文档翻译任务列表
- `getRunningTasks()`: 获取需要更新状态的任务（运行中的任务）
- `deleteExpiredTasks()`: 删除过期的任务
- `getTaskStatistics()`: 获取任务统计信息
- `batchUpdateTaskStatus()`: 批量更新任务状态

### 3. 翻译服务集成

#### BaiduTranslationService 扩展

**新增方法**:
- `smartTranslateDocument()`: 智能文档翻译（集成数据库记录）
- `queryDocumentTranslationWithUpdate()`: 查询文档翻译状态并更新数据库记录
- `mapBaiduStatusToDbStatus()`: 映射百度翻译状态到数据库状态
- `getUserDocumentTasks()`: 获取用户的文档翻译任务列表
- `getUserDocumentTaskStatistics()`: 获取用户的文档翻译统计信息

**集成特性**:
- 创建翻译任务时自动创建数据库记录
- 查询任务状态时自动更新数据库记录
- 状态映射和时间戳管理
- 错误处理和日志记录

### 4. API控制器扩展

#### TranslationController 新增端点

**文档翻译管理**:
- `translateDocument()`: 创建文档翻译任务（返回数据库任务ID）
- `queryDocumentTranslation()`: 查询文档翻译状态（同时更新数据库）
- `getDocumentTasks()`: 获取用户的文档翻译任务列表
- `getDocumentTaskStatistics()`: 获取用户的文档翻译统计信息

**新增路由**:
- `GET /document-tasks`: 获取文档翻译任务列表
- `GET /document-statistics`: 获取文档翻译统计信息

### 5. 定时任务调度

#### DocumentTranslationSchedulerService (`src/services/document-translation-scheduler.service.ts`)

**核心功能**:
- 定期检查运行中的文档翻译任务状态
- 自动更新任务状态和结果信息
- 并发处理任务（限制并发数）
- 清理过期的任务记录

**调度特性**:
- 可配置的检查间隔（默认5分钟）
- 启动/停止控制
- 手动触发检查
- 状态监控和日志记录

### 6. 数据库迁移

#### CreateDocumentTranslationTaskTable (`src/migrations/1640995200000-CreateDocumentTranslationTaskTable.ts`)

**表结构**:
- 完整的字段定义
- 索引优化（外部任务ID、用户ID、状态、创建时间）
- 外键约束（关联用户账号）
- 枚举类型定义

## 🔄 工作流程

### 1. 创建文档翻译任务
```
用户提交文档 → 调用百度API创建任务 → 创建数据库记录 → 返回任务ID
```

### 2. 查询任务状态
```
用户查询状态 → 调用百度API查询 → 更新数据库记录 → 返回最新状态
```

### 3. 定时状态更新
```
定时器触发 → 获取运行中任务 → 批量查询状态 → 更新数据库记录
```

### 4. 任务生命周期
```
NotStarted → Running → Succeeded/Failed/Expired
```

## 📊 数据库表结构

```sql
CREATE TABLE `document_translation_tasks` (
  `id` varchar(36) PRIMARY KEY COMMENT '主键ID',
  `externalTaskId` varchar(100) NOT NULL COMMENT '外部任务ID',
  `accountId` varchar(36) NOT NULL COMMENT '用户账号ID',
  `vendor` varchar(20) DEFAULT 'baidu' COMMENT '翻译供应商',
  `originalFilename` varchar(255) NOT NULL COMMENT '原始文件名',
  `fileFormat` varchar(10) NOT NULL COMMENT '文件格式',
  `fileSize` bigint NOT NULL COMMENT '文件大小（字节）',
  `sourceLanguage` varchar(10) NOT NULL COMMENT '源语言',
  `targetLanguage` varchar(10) NOT NULL COMMENT '目标语言',
  `domain` varchar(50) NULL COMMENT '垂直领域',
  `outputFormats` json NULL COMMENT '输出格式列表',
  `filenamePrefix` varchar(100) NULL COMMENT '文件名前缀',
  `status` enum('NotStarted','Running','Succeeded','Failed','Expired') DEFAULT 'NotStarted' COMMENT '任务状态',
  `reason` text NULL COMMENT '状态说明',
  `resultFiles` json NULL COMMENT '翻译结果文件信息',
  `characterCount` int NULL COMMENT '字符数量',
  `startedAt` datetime NULL COMMENT '任务开始时间',
  `completedAt` datetime NULL COMMENT '任务完成时间',
  `expiredAt` datetime NULL COMMENT '任务过期时间',
  `errorMessage` text NULL COMMENT '错误信息',
  `rawResponse` json NULL COMMENT '原始API响应数据',
  `createdAt` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX `IDX_external_task_id` (`externalTaskId`),
  INDEX `IDX_account_id` (`accountId`),
  INDEX `IDX_status` (`status`),
  INDEX `IDX_created_at` (`createdAt`),
  
  FOREIGN KEY `FK_account_id` (`accountId`) REFERENCES `accounts` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
);
```

## 🎯 API使用示例

### 1. 创建文档翻译任务
```typescript
POST /api/client/translation/translate-document
{
  "documentBase64": "base64_encoded_document",
  "from": "zh",
  "to": "en",
  "format": "docx",
  "filename": "document.docx",
  "vendor": "baidu",
  "domain": "general",
  "outputFormats": ["docx", "pdf"],
  "filenamePrefix": "translated_"
}

Response:
{
  "code": 200,
  "data": {
    "taskId": "external_task_id",
    "dbTaskId": "database_task_id",
    "message": "文档翻译任务创建成功"
  }
}
```

### 2. 查询任务状态
```typescript
POST /api/client/translation/query-document
{
  "taskId": "external_task_id",
  "vendor": "baidu"
}

Response:
{
  "code": 200,
  "data": {
    "id": "external_task_id",
    "status": "Succeeded",
    "reason": "翻译完成",
    "output": {
      "files": [
        {
          "format": "docx",
          "filename": "translated_document.docx",
          "url": "download_url"
        }
      ]
    }
  }
}
```

### 3. 获取任务列表
```typescript
GET /api/client/translation/document-tasks?status=Succeeded&limit=10&offset=0

Response:
{
  "code": 200,
  "data": {
    "tasks": [...],
    "total": 25,
    "limit": 10,
    "offset": 0
  }
}
```

### 4. 获取统计信息
```typescript
GET /api/client/translation/document-statistics

Response:
{
  "code": 200,
  "data": {
    "total": 100,
    "notStarted": 5,
    "running": 10,
    "succeeded": 80,
    "failed": 3,
    "expired": 2
  }
}
```

## 🔧 部署和配置

### 1. 数据库迁移
```bash
# 运行迁移创建表
npm run migration:run
```

### 2. 启动定时任务
```typescript
import { DocumentTranslationSchedulerService } from './services/document-translation-scheduler.service';

const scheduler = new DocumentTranslationSchedulerService();
scheduler.start(5); // 每5分钟检查一次
```

### 3. 环境配置
```env
# 文档翻译配置
DOCUMENT_TRANSLATION_CHECK_INTERVAL=5  # 检查间隔（分钟）
DOCUMENT_TRANSLATION_CONCURRENCY=5     # 并发处理数量
```

## 📈 性能优化

### 1. 数据库优化
- 索引优化：外部任务ID、用户ID、状态、创建时间
- 分页查询：支持limit和offset
- 批量操作：批量更新任务状态

### 2. 并发控制
- 限制并发查询数量（默认5个）
- 分块处理任务列表
- 异步处理和错误隔离

### 3. 缓存策略
- 任务状态缓存
- 用户统计信息缓存
- API响应缓存

## 🔍 监控和维护

### 1. 日志记录
- 任务创建日志
- 状态更新日志
- 错误处理日志
- 性能监控日志

### 2. 数据清理
- 定期清理过期任务
- 清理无效的任务记录
- 归档历史数据

### 3. 故障排查
- 任务状态异常检测
- API调用失败处理
- 数据库连接监控

---

## 📝 总结

文档翻译任务的数据库记录功能已经完全实现，包括：

✅ **完整的数据库实体和表结构**
✅ **数据库服务层和业务逻辑**
✅ **翻译服务集成和状态同步**
✅ **API控制器和路由扩展**
✅ **定时任务调度和状态更新**
✅ **数据库迁移和部署配置**

该系统提供了完整的文档翻译任务生命周期管理，支持任务跟踪、状态监控、统计分析等功能，为用户提供了可靠的文档翻译服务。

**实现完成时间**: 2025-07-29
**文档版本**: v1.0
**维护人员**: AI Assistant
