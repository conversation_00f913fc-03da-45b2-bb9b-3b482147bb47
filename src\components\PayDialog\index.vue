<template>
    <el-dialog v-model="dialogVisible" title="订单支付" width="400" @close="handleClose" :close-on-click-modal="false">
        <div class="flex flex-col items-center">
            <div class="h-[300px] w-[300px] bg-slate-400"></div>
            <div class="mt-4 text-[red] font-bold text-[20px]">￥{{ ((order?.amount || 0) / 100).toFixed(2) }}</div>
            <div class="mt-4">请使用支付宝扫码支付</div>
            <div class="mt-4">
                <el-button @click="handleClose">取消支付</el-button>
                <el-button type="primary" @click="handleIsPay">已完成支付</el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { getMembershipOrderDetailAPI, MembershipOrder } from '@/api/membership'
import { $message } from '@/utils/message'
import { ref } from 'vue'

const dialogVisible = ref(false)
const order = ref<MembershipOrder | undefined>(undefined)
const codeImg = ref<string>('')

const open = async (orderNo: string, img: string) => {
    const res = await getMembershipOrderDetailAPI(orderNo)
    if (res.data.status === 'paid') {
        $message.success('支付成功')
        return
    }
    if (res.data.status === 'cancelled') {
        $message.warning('订单已取消')
        return
    }
    if (res.data.status === 'refunded') {
        $message.warning('订单已退款')
        return
    }
    if (res.data.status === 'pending') {
        order.value = res.data
        codeImg.value = img
        dialogVisible.value = true
    }
}

const handleClose = () => {
    // 清理操作
    dialogVisible.value = false
}

const handleIsPay = async () => {
    const res = await getMembershipOrderDetailAPI(order.value?.orderNo || '')
    if (res.data.status === 'paid') {
        // 支付成功
        dialogVisible.value = false
        $message.success('支付成功')
    } else {
        $message.warning('订单未支付')
    }
}

defineExpose({
    open
})

</script>
