import Router from 'koa-router';
import { AdminAuthController } from '../../controllers/admin/auth.controller';
import { adminAuthMiddleware } from '../../middleware/auth.middleware';

const router = new Router({ prefix: '/api/admin/auth' });
const adminAuthController = new AdminAuthController();

// 公开路由
router.get('/captcha', adminAuthController.getCaptcha);
router.post('/login', adminAuthController.login);
router.post('/forgot-password', adminAuthController.forgotPassword);
router.post('/reset-password', adminAuthController.resetPassword);

// 需要认证的路由
router.get('/profile', adminAuthMiddleware, adminAuthController.getProfile);
router.put('/profile', adminAuthMiddleware, adminAuthController.updateProfile);
router.put('/password', adminAuthMiddleware, adminAuthController.changePassword);

export default router;
