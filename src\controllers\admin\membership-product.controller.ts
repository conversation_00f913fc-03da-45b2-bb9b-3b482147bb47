import { Context } from 'koa';
import { MembershipProductService } from '../../services/membership-product.service';

interface MembershipProductBody {
  name: string;
  description?: string;
  price: number;
  duration: number;
  sort?: number;
  isActive?: boolean;
  iconUrl?: string;
  permissionIds?: string[];
}

/**
 * 管理员会员商品控制器
 */
export class AdminMembershipProductController {
  private membershipProductService: MembershipProductService;

  constructor() {
    this.membershipProductService = new MembershipProductService();
  }

  /**
   * 获取会员商品列表
   */
  public getList = async (ctx: Context): Promise<void> => {
    try {
      const page = parseInt(ctx.query.page as string) || 1;
      const pageSize = parseInt(ctx.query.pageSize as string) || 10;

      const result = await this.membershipProductService.findPage(page, pageSize, {
        order: { sort: 'ASC', createdAt: 'DESC' },
      });

      ctx.body = {
        code: 0,
        data: result,
        message: '获取会员商品列表成功',
      };
    } catch (error) {
      console.error('获取会员商品列表失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取会员商品列表失败',
      };
    }
  };

  /**
   * 获取会员商品详情
   */
  public getDetail = async (ctx: Context): Promise<void> => {
    try {
      const { id } = ctx.params;

      const result = await this.membershipProductService.getMembershipProductDetail(id);

      if (!result) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '会员商品不存在',
        };
        return;
      }

      ctx.body = {
        code: 0,
        data: result,
        message: '获取会员商品详情成功',
      };
    } catch (error) {
      console.error('获取会员商品详情失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取会员商品详情失败',
      };
    }
  };

  /**
   * 创建会员商品
   */
  public create = async (ctx: Context): Promise<void> => {
    try {
      const { name, description, price, duration, sort, isActive, iconUrl, permissionIds } = ctx
        .request.body as MembershipProductBody;

      // 验证必填字段
      if (!name || !price || !duration) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '请填写所有必填字段',
        };
        return;
      }

      const result = await this.membershipProductService.createMembershipProduct({
        name,
        description,
        price,
        duration,
        sort,
        isActive,
        iconUrl,
        permissionIds,
      });

      ctx.body = {
        code: 0,
        data: result,
        message: '创建会员商品成功',
      };
    } catch (error) {
      console.error('创建会员商品失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '创建会员商品失败',
      };
    }
  };

  /**
   * 更新会员商品
   */
  public update = async (ctx: Context): Promise<void> => {
    try {
      const { id } = ctx.params;
      const { name, description, price, duration, sort, isActive, iconUrl, permissionIds } = ctx
        .request.body as MembershipProductBody;

      const result = await this.membershipProductService.updateMembershipProduct(id, {
        name,
        description,
        price,
        duration,
        sort,
        isActive,
        iconUrl,
        permissionIds,
      });

      if (!result) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '会员商品不存在',
        };
        return;
      }

      ctx.body = {
        code: 0,
        data: result,
        message: '更新会员商品成功',
      };
    } catch (error) {
      console.error('更新会员商品失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '更新会员商品失败',
      };
    }
  };

  /**
   * 删除会员商品
   */
  public delete = async (ctx: Context): Promise<void> => {
    try {
      const { id } = ctx.params;

      const result = await this.membershipProductService.remove(id);

      if (!result) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '会员商品不存在',
        };
        return;
      }

      ctx.body = {
        code: 0,
        message: '删除会员商品成功',
      };
    } catch (error) {
      console.error('删除会员商品失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '删除会员商品失败',
      };
    }
  };
}
