import { Router } from "vue-router";

/**
 * 第三方聊天页面路由守卫
 * @param router
 */
function setupChatViewGuard(router: Router) {
    router.beforeEach((to, from) => {
        if (to.name === 'chat') {
            window.ipcRenderer?.invoke('showChatWindow')
        } else if (from.name === 'chat') {
            window.ipcRenderer?.invoke('hideChatWindow')
        }
    });
}

/**
 * 项目守卫配置
 * @param router
 */
function createRouterGuard(router: Router) {
    /** 第三方聊天页面 */
    setupChatViewGuard(router);
}

export { createRouterGuard };