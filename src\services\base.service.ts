import {
  DeepPartial,
  FindManyOptions,
  FindOneOptions,
  FindOptionsWhere,
  ObjectLiteral,
  Repository,
} from 'typeorm';
import { addTenantCondition } from '../utils/tenant.util';

/**
 * 基础服务类
 * 提供常用的CRUD操作
 */
export class BaseService<T extends ObjectLiteral> {
  protected repository: Repository<T>;

  constructor(repository: Repository<T>) {
    this.repository = repository;
  }

  /**
   * 获取单个实体
   */
  async findOne(id: string): Promise<T | null> {
    const where = { id } as unknown as FindOptionsWhere<T>;
    const tenantWhere = addTenantCondition<any>(where);
    return this.repository.findOne({ where: tenantWhere } as FindOneOptions<T>);
  }

  /**
   * 获取实体列表
   */
  async findAll(options?: FindManyOptions<T>): Promise<T[]> {
    const opts = { ...options } as FindManyOptions<T>;
    if (opts.where) {
      opts.where = addTenantCondition<any>(opts.where as FindOptionsWhere<T>);
    } else {
      opts.where = addTenantCondition<any>(undefined);
    }
    return this.repository.find(opts);
  }

  /**
   * 分页查询
   */
  async findPage(
    page = 1,
    pageSize = 10,
    options?: FindManyOptions<T>,
  ): Promise<{ items: T[]; total: number; page: number; pageSize: number; totalPages: number }> {
    const opts = { ...options } as FindManyOptions<T>;
    if (opts.where) {
      opts.where = addTenantCondition<any>(opts.where as FindOptionsWhere<T>);
    } else {
      opts.where = addTenantCondition<any>(undefined);
    }

    const [items, total] = await this.repository.findAndCount({
      ...opts,
      skip: (page - 1) * pageSize,
      take: pageSize,
    });

    return {
      items,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  /**
   * 创建实体
   */
  async create(data: DeepPartial<T>): Promise<T> {
    const entity = this.repository.create(data);
    return this.repository.save(entity);
  }

  /**
   * 更新实体
   */
  async update(id: string, data: DeepPartial<T>): Promise<boolean> {
    // 先查询实体是否存在
    const entity = await this.findOne(id);
    if (!entity) {
      return false;
    }

    // 更新实体
    const result = await this.repository.update(id, data as any);
    return result && result.affected ? result.affected > 0 : false;
  }

  /**
   * 删除实体
   */
  async remove(id: string): Promise<boolean> {
    // 先查询实体是否存在
    const entity = await this.findOne(id);
    if (!entity) {
      return false;
    }

    // 删除实体
    const result = await this.repository.delete(id);
    return result && result.affected ? result.affected > 0 : false;
  }
}
