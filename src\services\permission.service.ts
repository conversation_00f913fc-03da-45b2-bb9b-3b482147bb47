import { AppDataSource } from '../config/database';
import { BaseService } from './base.service';
import { Permission } from '../entities/Permission';

/**
 * 权限服务
 */
export class PermissionService extends BaseService<Permission> {
  constructor() {
    super(AppDataSource.getRepository(Permission));
  }

  /**
   * 根据权限标识获取权限
   */
  async getPermissionByKey(key: string): Promise<Permission | null> {
    return this.repository.findOne({
      where: { key },
    });
  }

  /**
   * 获取权限列表（按分组返回）
   */
  async getPermissionsByGroup(): Promise<Record<string, Permission[]>> {
    const permissions = await this.repository.find({
      where: { isActive: true },
      order: { group: 'ASC' },
    });

    // 按分组返回
    const result: Record<string, Permission[]> = {};
    permissions.forEach((permission) => {
      const group = permission.group || '默认分组';
      if (!result[group]) {
        result[group] = [];
      }
      result[group].push(permission);
    });

    return result;
  }

  /**
   * 检查权限标识是否已存在
   */
  async isKeyExists(key: string, excludeId?: string): Promise<boolean> {
    const query = this.repository
      .createQueryBuilder('permission')
      .where('permission.key = :key', { key });

    if (excludeId) {
      query.andWhere('permission.id != :id', { id: excludeId });
    }

    const count = await query.getCount();
    return count > 0;
  }
}
