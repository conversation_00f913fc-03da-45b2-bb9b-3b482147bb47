import { DataSource } from 'typeorm';
import { Account } from '../entities/Account';
import { Client } from '../entities/Client';
import { MembershipProduct } from '../entities/MembershipProduct';
import { Permission } from '../entities/Permission';
import { MembershipPermission } from '../entities/MembershipPermission';
import { ClientMembership } from '../entities/ClientMembership';
import { MembershipOrder } from '../entities/MembershipOrder';
import { ChatAccount } from '../entities/ChatAccount';
import path from 'path';
import { TranslationRoute } from '../entities/TranslationRoute';
import { DocumentTranslationTask } from '../entities/DocumentTranslationTask';
import { PointsProduct } from '../entities/PointsProduct';
import { PointsOrder } from '../entities/PointsOrder';
import { PointsTransaction } from '../entities/PointsTransaction';
import { envConfig } from '../utils/env.util';
envConfig();

const isDevelopment = process.env.NODE_ENV !== 'production';

// 创建 TypeORM 数据源
export const AppDataSource = new DataSource({
  type: 'mysql',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306', 10),
  username: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || 'root',
  database: process.env.DB_DATABASE || 'scrm',
  synchronize: true, // 开发环境下启用自动同步
  dropSchema: false, // 禁用自动删除旧表
  logging: isDevelopment,
  entities: [
    Account,
    Client,
    MembershipProduct,
    Permission,
    MembershipPermission,
    ClientMembership,
    MembershipOrder,
    ChatAccount,
    TranslationRoute,
    DocumentTranslationTask,
    PointsProduct,
    PointsOrder,
    PointsTransaction,
  ], // 添加所有实体
  subscribers: [path.join(__dirname, '../subscribers/*.ts')],
  migrations: [path.join(__dirname, '../migrations/*.ts')], // 迁移文件路径
  migrationsTableName: 'migrations', // 迁移记录表名
});
