import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Client } from './Client';
import { PointsProduct } from './PointsProduct';

export enum PointsOrderStatus {
  PENDING = 'pending',
  PAID = 'paid',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired',
}

/**
 * 积分订单实体
 */
@Entity('points_orders')
export class PointsOrder {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @CreateDateColumn({ type: 'datetime' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'datetime' })
  updatedAt: Date;

  @Column({ unique: true, length: 32, comment: '订单号' })
  orderNo: string;

  @Column({ name: 'client_id', comment: '用户ID' })
  clientId: string;

  @ManyToOne(() => Client)
  @JoinColumn({ name: 'client_id' })
  client: Client;

  @Column({ name: 'product_id', comment: '商品ID' })
  productId: string;

  @ManyToOne(() => PointsProduct)
  @JoinColumn({ name: 'product_id' })
  product: PointsProduct;

  @Column({ type: 'int', comment: '原价（分）' })
  originalPrice: number;

  @Column({ type: 'int', comment: '折扣价（分）' })
  discountPrice: number;

  @Column({ type: 'int', comment: '实际支付价格（分）' })
  actualPrice: number;

  @Column({ type: 'int', comment: '获得积分' })
  points: number;

  @Column({ type: 'int', default: 0, comment: '赠送积分' })
  bonusPoints: number;

  @Column({
    type: 'enum',
    enum: PointsOrderStatus,
    default: PointsOrderStatus.PENDING,
    comment: '订单状态',
  })
  status: PointsOrderStatus;

  @Column({ length: 50, nullable: true, comment: '支付方式' })
  paymentMethod?: string;

  @Column({ length: 100, nullable: true, comment: '交易ID' })
  transactionId?: string;

  @Column({ type: 'datetime', nullable: true, comment: '支付时间' })
  paidAt?: Date;

  @Column({ type: 'datetime', nullable: true, comment: '过期时间' })
  expiredAt?: Date;

  /**
   * 计算总积分（基础积分 + 赠送积分）
   */
  get totalPoints(): number {
    return this.points + this.bonusPoints;
  }

  /**
   * 检查订单是否已过期
   */
  get isExpired(): boolean {
    return this.expiredAt ? new Date() > this.expiredAt : false;
  }
}
