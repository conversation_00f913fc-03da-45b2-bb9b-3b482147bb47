import Redis from 'ioredis';
import { REDIS_CONFIG } from '../config/redis';

export class RedisService {
  private client: Redis;

  constructor() {
    this.client = new Redis(REDIS_CONFIG);
  }

  async set(key: string, value: string, expireSeconds?: number): Promise<void> {
    if (expireSeconds) {
      await this.client.setex(key, expireSeconds, value);
    } else {
      await this.client.set(key, value);
    }
  }

  async get(key: string): Promise<string | null> {
    return await this.client.get(key);
  }

  async del(key: string): Promise<void> {
    await this.client.del(key);
  }
}
