import { DocumentTranslationTaskService } from './document-translation-task.service';
import { BaiduTranslationService } from './baidu-translation.service';
import { DocumentTranslationStatus } from '../entities/DocumentTranslationTask';

/**
 * 文档翻译任务调度服务
 * 负责定期检查和更新运行中的文档翻译任务状态
 */
export class DocumentTranslationSchedulerService {
  private taskService: DocumentTranslationTaskService;
  private baiduService: BaiduTranslationService;
  private isRunning: boolean = false;
  private intervalId: NodeJS.Timeout | null = null;

  constructor() {
    this.taskService = new DocumentTranslationTaskService();
    this.baiduService = new BaiduTranslationService();
  }

  /**
   * 启动定时任务
   * @param intervalMinutes 检查间隔（分钟），默认5分钟
   */
  start(intervalMinutes: number = 5): void {
    if (this.isRunning) {
      console.log('文档翻译任务调度器已在运行中');
      return;
    }

    this.isRunning = true;
    const intervalMs = intervalMinutes * 60 * 1000;

    console.log(`启动文档翻译任务调度器，检查间隔: ${intervalMinutes}分钟`);

    // 立即执行一次
    this.checkAndUpdateTasks();

    // 设置定时器
    this.intervalId = setInterval(() => {
      this.checkAndUpdateTasks();
    }, intervalMs);
  }

  /**
   * 停止定时任务
   */
  stop(): void {
    if (!this.isRunning) {
      console.log('文档翻译任务调度器未在运行');
      return;
    }

    this.isRunning = false;
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    console.log('文档翻译任务调度器已停止');
  }

  /**
   * 检查并更新运行中的任务状态
   */
  private async checkAndUpdateTasks(): Promise<void> {
    try {
      console.log('开始检查文档翻译任务状态...');

      // 获取所有运行中的任务
      const runningTasks = await this.taskService.getRunningTasks();

      if (runningTasks.length === 0) {
        console.log('没有运行中的文档翻译任务');
        return;
      }

      console.log(`发现 ${runningTasks.length} 个运行中的文档翻译任务`);

      // 并发处理任务（限制并发数）
      const concurrencyLimit = 5;
      const chunks = this.chunkArray(runningTasks, concurrencyLimit);

      for (const chunk of chunks) {
        await Promise.all(
          chunk.map(async (task) => {
            try {
              await this.updateTaskStatus(task);
            } catch (error) {
              console.error(`更新任务状态失败 (任务ID: ${task.externalTaskId}):`, error);
            }
          }),
        );
      }

      console.log('文档翻译任务状态检查完成');
    } catch (error) {
      console.error('检查文档翻译任务状态时发生错误:', error);
    }
  }

  /**
   * 更新单个任务的状态
   */
  private async updateTaskStatus(task: any): Promise<void> {
    try {
      console.log(`检查任务状态: ${task.externalTaskId}`);

      // 获取用户的百度文档翻译线路
      const routes = await this.baiduService.getAvailableBaiduDocRoutes(task.clientId);

      if (routes.length === 0) {
        console.warn(`用户 ${task.clientId} 没有可用的百度文档翻译线路`);
        return;
      }

      // 使用第一个可用线路查询状态
      const route = routes[0];
      const result = await this.baiduService.queryDocumentTranslation(task.externalTaskId, route);

      if (result?.data) {
        const newStatus = this.mapBaiduStatusToDbStatus(result.data.status);

        // 只有状态发生变化时才更新
        if (newStatus !== task.status) {
          await this.taskService.updateTaskStatus(task.externalTaskId, newStatus, {
            reason: result.data.reason,
            resultFiles: result.data.output?.files,
            characterCount: result.data.input.character_count,
            startedAt: result.data.created_at ? new Date(result.data.created_at * 1000) : undefined,
            completedAt: result.data.updated_at
              ? new Date(result.data.updated_at * 1000)
              : undefined,
            expiredAt: result.data.expired_at ? new Date(result.data.expired_at * 1000) : undefined,
            rawResponse: result,
          });

          console.log(`任务状态已更新: ${task.externalTaskId} (${task.status} -> ${newStatus})`);
        } else {
          console.log(`任务状态无变化: ${task.externalTaskId} (${task.status})`);
        }
      } else {
        console.warn(`无法获取任务状态: ${task.externalTaskId}`);
      }
    } catch (error) {
      console.error(`更新任务状态失败 (${task.externalTaskId}):`, error);

      // 如果查询失败多次，可以考虑标记为失败状态
      // 这里可以添加重试逻辑或失败计数
    }
  }

  /**
   * 映射百度翻译状态到数据库状态
   */
  private mapBaiduStatusToDbStatus(baiduStatus: string): DocumentTranslationStatus {
    switch (baiduStatus) {
      case 'NotStarted':
        return DocumentTranslationStatus.NOT_STARTED;
      case 'Running':
        return DocumentTranslationStatus.RUNNING;
      case 'Succeeded':
        return DocumentTranslationStatus.SUCCEEDED;
      case 'Failed':
        return DocumentTranslationStatus.FAILED;
      case 'Expired':
        return DocumentTranslationStatus.EXPIRED;
      default:
        return DocumentTranslationStatus.NOT_STARTED;
    }
  }

  /**
   * 将数组分块
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * 清理过期的任务记录
   */
  async cleanupExpiredTasks(): Promise<void> {
    try {
      console.log('开始清理过期的文档翻译任务...');

      const deletedCount = await this.taskService.deleteExpiredTasks();

      if (deletedCount > 0) {
        console.log(`已清理 ${deletedCount} 个过期的文档翻译任务`);
      } else {
        console.log('没有需要清理的过期任务');
      }
    } catch (error) {
      console.error('清理过期任务时发生错误:', error);
    }
  }

  /**
   * 获取调度器状态
   */
  getStatus(): {
    isRunning: boolean;
    intervalId: NodeJS.Timeout | null;
  } {
    return {
      isRunning: this.isRunning,
      intervalId: this.intervalId,
    };
  }

  /**
   * 手动触发一次任务检查
   */
  async triggerCheck(): Promise<void> {
    if (!this.isRunning) {
      throw new Error('调度器未运行，无法手动触发检查');
    }

    await this.checkAndUpdateTasks();
  }
}
