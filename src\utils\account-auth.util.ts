import bcrypt from 'bcryptjs';
import jwt, { Secret } from 'jsonwebtoken';
import { AccountJwtPayload, ClientJwtPayload } from '../types/auth.interface';
import { Account } from '../entities/Account';
import { Client } from '../entities/Client';
import { JWT_SECRET, JWT_EXPIRES_IN } from '../config/jwt';

/**
 * 生成密码哈希
 */
export const hashPassword = async (password: string): Promise<string> => {
  const salt = await bcrypt.genSalt(10);
  return bcrypt.hash(password, salt);
};

/**
 * 验证密码
 */
export const comparePassword = async (
  password: string,
  hashedPassword: string,
): Promise<boolean> => {
  return bcrypt.compare(password, hashedPassword);
};

/**
 * 生成管理员账号JWT令牌
 */
export const generateAccountToken = (account: Account): string => {
  const payload: AccountJwtPayload = {
    id: account.id,
    email: account.email,
    name: account.name,
    isSystemAdmin: account.isSystemAdmin,
    tenantId: account.tenantId,
    type: 'account',
  };

  const secret = (JWT_SECRET || 'your_jwt_secret') as Secret;

  return jwt.sign(payload, secret, {
    expiresIn: (JWT_EXPIRES_IN || '7d') as any,
  });
};

/**
 * 生成客户端用户JWT令牌
 */
export const generateClientToken = (client: Client): string => {
  const payload: ClientJwtPayload = {
    id: client.id,
    email: client.email,
    name: client.name,
    tenantId: client.tenantId,
    type: 'client',
  };

  const secret = (JWT_SECRET || 'your_jwt_secret') as Secret;

  return jwt.sign(payload, secret, {
    expiresIn: (JWT_EXPIRES_IN || '7d') as any,
  });
};

/**
 * 验证JWT令牌
 */
export const verifyToken = <T extends AccountJwtPayload | ClientJwtPayload>(token: string): T => {
  const secret = (JWT_SECRET || 'your_jwt_secret') as Secret;
  return jwt.verify(token, secret) as T;
};
