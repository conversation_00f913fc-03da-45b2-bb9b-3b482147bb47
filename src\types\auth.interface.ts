export interface LoginDto {
  email: string;
  password: string;
}

export interface RegisterDto extends LoginDto {
  name?: string;
  tenantId?: string;
}

export interface JwtPayload {
  id: string;
  email: string;
  tenantId: string;
  iat?: number;
  exp?: number;
}

// 管理员账号登录DTO
export interface AccountLoginDto {
  email: string;
  password: string;
}

// 管理员JWT载荷
export interface AccountJwtPayload {
  id: string;
  email: string;
  name: string;
  isSystemAdmin: boolean;
  tenantId: string;
  type: 'account';
  iat?: number;
  exp?: number;
}

// 客户端用户登录DTO
export interface ClientLoginDto {
  email: string;
  password: string;
}

// 客户端用户注册DTO
export interface ClientRegisterDto {
  email: string;
  password: string;
  name?: string;
  phone?: string;
}

// 客户端用户JWT载荷
export interface ClientJwtPayload {
  id: string;
  email: string;
  name: string;
  tenantId: string;
  type: 'client';
  iat?: number;
  exp?: number;
}
