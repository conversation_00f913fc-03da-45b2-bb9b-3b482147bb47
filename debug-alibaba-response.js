// 调试阿里云API响应结构
console.log('🔍 阿里云API响应结构调试工具');
console.log('');

console.log('📋 当前问题:');
console.log('- API响应中所有字段都是undefined');
console.log('- Code: undefined');
console.log('- Message: undefined');
console.log('- RequestId: undefined');
console.log('- hasData: false');
console.log('');

console.log('🔧 已实施的调试修复:');
console.log('1. ✅ 添加完整响应日志');
console.log('2. ✅ 检查响应状态码');
console.log('3. ✅ 检查响应头信息');
console.log('4. ✅ 检查响应体类型');
console.log('5. ✅ 检查响应体键名');
console.log('6. ✅ 输出完整响应JSON');
console.log('');

console.log('🎯 可能的原因分析:');
console.log('');

console.log('1. **SDK版本问题**');
console.log('   - 可能使用了不兼容的SDK版本');
console.log('   - 响应结构在不同版本间可能有变化');
console.log('');

console.log('2. **API端点问题**');
console.log('   - 当前使用: mt.cn-hangzhou.aliyuncs.com');
console.log('   - 可能需要使用不同的端点');
console.log('');

console.log('3. **响应结构变化**');
console.log('   - 阿里云可能更新了响应格式');
console.log('   - 字段名可能发生了变化');
console.log('');

console.log('4. **权限或配置问题**');
console.log('   - API密钥可能没有足够权限');
console.log('   - 服务可能未正确开通');
console.log('');

console.log('📊 预期的响应结构:');
const expectedResponse = {
    statusCode: 200,
    headers: { /* ... */ },
    body: {
        RequestId: 'D774D33D-F1CB-5A2C-A787-E0A2179239CE',
        Code: 200,
        Message: 'Success',
        Data: {
            FinalImageUrl: 'https://example.com/translated-image.jpg',
            InPaintingUrl: 'https://example.com/inpainting.jpg',
            TemplateJson: '{"template": "data"}'
        }
    }
};
console.log(JSON.stringify(expectedResponse, null, 2));
console.log('');

console.log('🔍 调试步骤:');
console.log('1. 重新测试图片翻译功能');
console.log('2. 查看服务端日志中的"阿里云API完整响应"');
console.log('3. 检查响应的实际结构');
console.log('4. 对比预期结构和实际结构的差异');
console.log('');

console.log('📝 可能的解决方案:');
console.log('');

console.log('**方案1: 检查SDK文档**');
console.log('- 查看@alicloud/alimt20181012的最新文档');
console.log('- 确认正确的响应结构');
console.log('- 更新响应处理逻辑');
console.log('');

console.log('**方案2: 尝试不同的API调用方式**');
console.log('- 使用不同的运行时选项');
console.log('- 尝试同步调用方式');
console.log('- 检查是否需要特殊的头部信息');
console.log('');

console.log('**方案3: 验证API密钥和权限**');
console.log('- 在阿里云控制台测试API密钥');
console.log('- 确认机器翻译服务状态');
console.log('- 检查账户余额和配额');
console.log('');

console.log('**方案4: 使用官方示例代码**');
console.log('- 下载阿里云官方SDK示例');
console.log('- 对比我们的实现和官方示例');
console.log('- 找出差异并修正');
console.log('');

console.log('🚨 紧急处理建议:');
console.log('如果阿里云API持续有问题，建议:');
console.log('1. 临时禁用阿里云图片翻译');
console.log('2. 只使用有道翻译作为图片翻译服务');
console.log('3. 在修复阿里云问题后再重新启用');
console.log('');

console.log('💡 临时禁用代码:');
console.log('```typescript');
console.log('// 在 translation.controller.ts 中');
console.log('if (vendor === VendorType.ALIBABA) {');
console.log('  ctx.status = 400;');
console.log('  ctx.body = {');
console.log('    code: 400,');
console.log('    message: "阿里云图片翻译暂时不可用，请使用有道翻译"');
console.log('  };');
console.log('  return;');
console.log('}');
console.log('```');
console.log('');

console.log('🔄 下一步行动:');
console.log('1. 运行图片翻译测试');
console.log('2. 查看详细的响应日志');
console.log('3. 根据实际响应结构调整代码');
console.log('4. 如果问题持续，考虑联系阿里云技术支持');
console.log('');

console.log('✅ 调试工具准备完成，请查看服务端日志！');
