# SCRM API 接口文档

## 目录

- [通用信息](#通用信息)
- [管理端接口](#管理端接口)
  - [认证模块](./admin-auth.md) - 管理员登录、验证码、密码管理
  - [会员管理模块](./admin-membership.md) - 会员商品管理、权限管理
- [客户端接口](#客户端接口)
  - [认证模块](./client-auth.md) - 用户注册、登录、个人资料管理
  - [会员模块](./client-membership.md) - 会员商品、订单管理、支付
  - [聊天账户模块](./client-chat-account.md) - 聊天账户的增删改查
  - [翻译模块](./client-translation.md) - 文本翻译、图片翻译、语言列表
  - [翻译路由模块](./client-translation-route.md) - 翻译路由配置管理

## 通用信息

### 基础URL

所有API的基础URL为: `http://localhost:3000/api`

### 认证方式

除了登录、注册等公开接口外，其他接口需要在请求头中添加 Bearer 令牌：

```
Authorization: Bearer {token}
```

### 响应格式

所有API响应均为JSON格式，基本结构如下：

```json
{
  "code": 200, // 状态码，200表示成功
  "message": "成功", // 状态消息
  "data": {} // 响应数据，可能是对象、数组或null
}
```

#### 分页响应结构

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "items": [], // 数据列表
    "total": 0, // 总记录数
    "page": 1, // 当前页码
    "pageSize": 10, // 每页条数
    "totalPages": 0 // 总页数
  }
}
```

### 常见错误码

| 状态码 | 描述             |
| ------ | ---------------- |
| 200    | 成功             |
| 400    | 请求参数错误     |
| 401    | 未授权或认证失败 |
| 403    | 权限不足         |
| 404    | 资源不存在       |
| 500    | 服务器内部错误   |

## 管理端接口

管理端接口主要供系统管理员使用，用于管理整个系统的配置和数据。

### 模块列表

- **[认证模块](./admin-auth.md)** - 管理员登录、验证码、密码管理等功能
- **[会员管理模块](./admin-membership.md)** - 会员商品管理、权限管理等功能

## 客户端接口

客户端接口主要供桌面应用程序使用，提供用户认证、会员管理、翻译服务等功能。

### 模块列表

- **[认证模块](./client-auth.md)** - 用户注册、登录、个人资料管理等功能
- **[会员模块](./client-membership.md)** - 会员商品查看、订单管理、支付等功能
- **[聊天账户模块](./client-chat-account.md)** - 聊天账户的创建、管理等功能
- **[翻译模块](./client-translation.md)** - 文本翻译、图片翻译、语言列表等功能
- **[翻译路由模块](./client-translation-route.md)** - 翻译路由配置管理等功能
