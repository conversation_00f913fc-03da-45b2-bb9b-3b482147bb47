<template>
    <div class="w-fit h-full flex">
        <TranslateConfig v-if="selected === IToolkeys.Translate" :chatId="chatId"></TranslateConfig>
        <ProxyConfig v-if="selected === IToolkeys.Proxy" :chatId="chatId" :chatInfo="chatInfo"></ProxyConfig>
        <FingerprintConfig v-if="selected === IToolkeys.Fingerprint" :chatId="chatId"></FingerprintConfig>
        <div class="w-[42px] flex flex-col items-center">
            <div class="flex flex-col items-center mb-4">
                <div>
                    <Menu :icon="GTranslateRound" size="20" :active="selected === IToolkeys.Translate"
                        @click="handleSelect(IToolkeys.Translate)"></Menu>
                </div>
                <div class="text-xs mt-1">翻译</div>
            </div>
            <div class="flex flex-col items-center mb-4">
                <div>
                    <Menu :icon="ServerProxy" size="20" :active="selected === IToolkeys.Proxy"
                        @click="handleSelect(IToolkeys.Proxy)"></Menu>
                </div>
                <div class="text-xs mt-1">代理</div>
            </div>
            <!-- <div class="flex flex-col items-center mb-4">
                <div>
                    <Menu :icon="FingerPrint" size="20" :active="selected === IToolkeys.Fingerprint"
                        @click="handleSelect(IToolkeys.Fingerprint)"></Menu>
                </div>
                <div class="text-xs mt-1">指纹</div>
            </div> -->
            <!-- <div class="flex flex-col items-center mb-4">
                <div>
                    <Menu :icon="QuickreplyOutlined" size="20" :active="selected === IToolkeys.Quickreply"
                        @click="handleSelect(IToolkeys.Quickreply)"></Menu>
                </div>
                <div class="text-xs mt-1">回复</div>
            </div> -->
        </div>
    </div>
</template>
<script setup lang="ts">
import { GTranslateRound, QuickreplyOutlined } from '@vicons/material';
import { ServerProxy } from '@vicons/carbon'
import { FingerPrint } from '@vicons/ionicons5'
import Menu from '@/components/Menu/index.vue';
import TranslateConfig from './TranslateConfig.vue'
import ProxyConfig from './ProxyConfig.vue'
import FingerprintConfig from './FingerprintConfig.vue'
import { ref } from 'vue';
import { IChatAccountsBo } from '@/api/chat';
enum IToolkeys {
    Translate = 'Translate',
    Proxy = 'Proxy',
    Fingerprint = 'Fingerprint',
    Quickreply = 'Quickreply'
}

defineProps<{
    chatId: string,
    chatInfo?: IChatAccountsBo
}>()

const selected = ref<IToolkeys | null>(null)
const handleSelect = (key: IToolkeys) => {
    if (selected.value === key) {
        selected.value = null;
    } else {
        selected.value = key;
    }
}
</script>

<style>
/* 自定义滚动条隐藏 */
.scrollbar-hide::-webkit-scrollbar {
    display: none;
}
</style>