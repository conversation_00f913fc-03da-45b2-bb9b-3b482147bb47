import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON>o<PERSON>ne, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseEntity } from './BaseEntity';
import { Client } from './Client';
import { MembershipProduct } from './MembershipProduct';

/**
 * 用户会员实体
 */
@Entity('client_memberships')
export class ClientMembership extends BaseEntity {
  @ManyToOne(() => Client)
  @JoinColumn({ name: 'client_id' })
  client!: Client;

  @ManyToOne(() => MembershipProduct)
  @JoinColumn({ name: 'membership_product_id' })
  membershipProduct!: MembershipProduct;

  @Column({
    comment: '会员开始时间',
    type: 'datetime',
  })
  startTime!: Date;

  @Column({
    comment: '会员结束时间',
    type: 'datetime',
  })
  endTime!: Date;

  @Column({
    comment: '会员状态: active-生效中, expired-已过期',
    length: 20,
    default: 'active',
  })
  status!: string;
}
