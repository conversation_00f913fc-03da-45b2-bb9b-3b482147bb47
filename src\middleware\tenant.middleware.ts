import { Context, Next } from 'koa';
import { setCurrentTenantId } from '../utils/tenant.util';

/**
 * 租户中间件
 * 从请求中提取租户ID并设置到当前上下文
 */
export const tenantMiddleware = async (ctx: Context, next: Next) => {
  try {
    // 1. 从请求头中获取租户ID
    let tenantId = ctx.headers['x-tenant-id'] as string;

    // 2. 如果请求头中没有租户ID，尝试从JWT令牌中获取
    if (!tenantId && ctx.state.user) {
      tenantId = ctx.state.user.tenantId;
    }

    // 3. 如果都没有，使用默认租户ID (0)
    if (!tenantId) {
      tenantId = '0';
    }

    // 4. 设置当前租户ID到租户上下文
    setCurrentTenantId(tenantId);

    // 5. 将租户ID添加到响应头中，方便调试
    ctx.set('X-Tenant-Id', tenantId);

    await next();
  } catch (error) {
    // 出错时，重置租户ID为默认值
    setCurrentTenantId('0');
    throw error;
  }
};
