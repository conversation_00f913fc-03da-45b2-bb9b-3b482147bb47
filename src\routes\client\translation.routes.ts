import Router from '@koa/router';
import { TranslationController } from '../../controllers/client/translation.controller';
import { tenantMiddleware } from '../../middleware/tenant.middleware';
import { clientAuthMiddleware } from '../../middleware/auth.middleware';

const router = new Router({ prefix: '/api/client/translation' });

router.use(tenantMiddleware).use(clientAuthMiddleware);

router
  .post('/translate', (ctx) => new TranslationController().translate(ctx))
  .post('/translate-image', (ctx) => new TranslationController().translateImage(ctx))
  .post('/translate-document', (ctx) => new TranslationController().translateDocument(ctx))
  .post('/query-document', (ctx) => new TranslationController().queryDocumentTranslation(ctx))
  .get('/document-tasks', (ctx) => new TranslationController().getDocumentTasks(ctx))
  .get('/document-statistics', (ctx) => new TranslationController().getDocumentTaskStatistics(ctx))
  .get('/routes', (ctx) => new TranslationController().getAvailableRoutes(ctx))
  .get('/languages', (ctx) => new TranslationController().getSupportedLanguages(ctx));

export default router;
