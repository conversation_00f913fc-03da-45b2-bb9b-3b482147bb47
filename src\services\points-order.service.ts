import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import { PointsOrder, PointsOrderStatus } from '../entities/PointsOrder';
import { PointsProduct } from '../entities/PointsProduct';
import { Client } from '../entities/Client';
import { PointsService, AddPointsOptions } from './points.service';
import {
  PointsTransactionType,
  PointsTransactionReferenceType,
} from '../entities/PointsTransaction';

export interface CreatePointsOrderOptions {
  clientId: string;
  productId: string;
}

/**
 * 积分订单服务
 */
export class PointsOrderService {
  private repository: Repository<PointsOrder>;
  private productRepository: Repository<PointsProduct>;
  private clientRepository: Repository<Client>;
  private pointsService: PointsService;

  constructor() {
    this.repository = AppDataSource.getRepository(PointsOrder);
    this.productRepository = AppDataSource.getRepository(PointsProduct);
    this.clientRepository = AppDataSource.getRepository(Client);
    this.pointsService = new PointsService();
  }

  /**
   * 生成订单号
   */
  private generateOrderNo(): string {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, '0');
    return `PO${timestamp}${random}`;
  }

  /**
   * 创建积分订单
   */
  async createOrder(options: CreatePointsOrderOptions): Promise<PointsOrder> {
    const { clientId, productId } = options;

    // 验证用户是否存在
    const client = await this.clientRepository.findOne({ where: { id: clientId } });
    if (!client) {
      throw new Error('用户不存在');
    }

    // 验证商品是否存在且启用
    const product = await this.productRepository.findOne({
      where: { id: productId, isActive: true },
    });
    if (!product) {
      throw new Error('积分商品不存在或已禁用');
    }

    // 创建订单
    const order = this.repository.create({
      orderNo: this.generateOrderNo(),
      clientId,
      productId,
      originalPrice: product.price,
      discountPrice: product.actualPrice,
      actualPrice: product.actualPrice,
      points: product.points,
      bonusPoints: product.bonusPoints,
      status: PointsOrderStatus.PENDING,
      expiredAt: new Date(Date.now() + 30 * 60 * 1000), // 30分钟后过期
    });

    return await this.repository.save(order);
  }

  /**
   * 根据订单号查找订单
   */
  async findByOrderNo(orderNo: string): Promise<PointsOrder | null> {
    return await this.repository.findOne({
      where: { orderNo },
      relations: ['client', 'product'],
    });
  }

  /**
   * 根据ID查找订单
   */
  async findById(id: string): Promise<PointsOrder | null> {
    return await this.repository.findOne({
      where: { id },
      relations: ['client', 'product'],
    });
  }

  /**
   * 获取用户订单列表
   */
  async findByClientId(
    clientId: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<{ orders: PointsOrder[]; total: number }> {
    const [orders, total] = await this.repository.findAndCount({
      where: { clientId },
      relations: ['product'],
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return { orders, total };
  }

  /**
   * 处理订单支付成功
   */
  async handlePaymentSuccess(
    orderNo: string,
    paymentMethod: string,
    transactionId: string,
  ): Promise<PointsOrder> {
    const order = await this.findByOrderNo(orderNo);
    if (!order) {
      throw new Error('订单不存在');
    }

    if (order.status === PointsOrderStatus.PAID) {
      return order; // 订单已支付，直接返回
    }

    if (order.status !== PointsOrderStatus.PENDING) {
      throw new Error('订单状态不正确，无法支付');
    }

    if (order.isExpired) {
      throw new Error('订单已过期');
    }

    // 开始事务处理
    return await AppDataSource.transaction(async (manager) => {
      // 更新订单状态
      order.status = PointsOrderStatus.PAID;
      order.paymentMethod = paymentMethod;
      order.transactionId = transactionId;
      order.paidAt = new Date();
      await manager.save(order);

      // 增加基础积分
      if (order.points > 0) {
        await this.pointsService.addPoints({
          clientId: order.clientId,
          amount: order.points,
          type: PointsTransactionType.RECHARGE,
          description: `购买积分商品：${order.product.name}`,
          referenceType: PointsTransactionReferenceType.ORDER,
          referenceId: order.id,
          metadata: {
            orderNo: order.orderNo,
            productName: order.product.name,
            basePoints: order.points,
          },
        });
      }

      // 增加赠送积分
      if (order.bonusPoints > 0) {
        await this.pointsService.addPoints({
          clientId: order.clientId,
          amount: order.bonusPoints,
          type: PointsTransactionType.BONUS,
          description: `积分商品赠送：${order.product.name}`,
          referenceType: PointsTransactionReferenceType.ORDER,
          referenceId: order.id,
          metadata: {
            orderNo: order.orderNo,
            productName: order.product.name,
            bonusPoints: order.bonusPoints,
          },
        });
      }

      return order;
    });
  }

  /**
   * 取消订单
   */
  async cancelOrder(orderNo: string): Promise<PointsOrder> {
    const order = await this.findByOrderNo(orderNo);
    if (!order) {
      throw new Error('订单不存在');
    }

    if (order.status !== PointsOrderStatus.PENDING) {
      throw new Error('只能取消待支付的订单');
    }

    order.status = PointsOrderStatus.CANCELLED;
    return await this.repository.save(order);
  }

  /**
   * 检查并处理过期订单
   */
  async handleExpiredOrders(): Promise<void> {
    const expiredOrders = await this.repository.find({
      where: {
        status: PointsOrderStatus.PENDING,
      },
    });

    const now = new Date();
    const ordersToExpire = expiredOrders.filter(
      (order) => order.expiredAt && now > order.expiredAt,
    );

    if (ordersToExpire.length > 0) {
      await this.repository.update(
        ordersToExpire.map((order) => order.id),
        { status: PointsOrderStatus.EXPIRED },
      );
    }
  }
}
