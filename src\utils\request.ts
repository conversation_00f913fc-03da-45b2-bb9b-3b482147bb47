import axios from 'axios'
import { getToken } from './auth'
import { toLoginWindow } from '../utils/windows'
import { $message, $confirm } from './message'
const request = axios.create({
    baseURL: import.meta.env.VITE_APP_BASE_URL,
    timeout: 10000,
});

request.interceptors.request.use(async (config) => {
    if (await getToken()) {
        // let each request carry token
        // ['X-Token'] is a custom headers key
        // please modify it according to the actual situation
        config.headers['Authorization'] = `Bearer ${await getToken()}`
    }
    return config;
}, error => {
    return Promise.reject(error);
})

request.interceptors.response.use(response => {
    const res = response.data
    // code不等于100则抛出异常信息
    if (res.code !== 200) {
        console.log(res.code);

        if (res.code === 401) {
            // to re-login
            toLoginWindow();
        } // 强提示
        else if (res.code === 102) {
            $confirm(res.message || 'Error', '温馨提示', {
                type: 'warning'
            })
        } else {
            // 轻提示
            $message?.error(res.message || 'Error')
        }
        return Promise.reject(new Error(res.message || 'Error'))
    } else {
        return res
    }
}, error => {
    console.log(error);
    const data = error.response.data
    const status = error.response.status
    if (status === 401) {
        // to re-login
        toLoginWindow();
    } else if (data.code === 102) {
        $confirm(data.message || 'Error', '温馨提示', {
            type: 'warning'
        })
    } else {
        // 轻提示
        $message?.error(data.message || 'Error')
    }
    return Promise.reject(error);
})

export default request;