import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from './BaseEntity';

/**
 * 主账号实体
 * 管理员端使用的主账号，可以管理多个子账号
 */
@Entity('accounts')
export class Account extends BaseEntity {
  @Column({
    length: 100,
    comment: '账号名称',
  })
  @Index()
  name!: string;

  @Column({
    unique: true,
    comment: '登录邮箱',
  })
  email!: string;

  @Column({
    comment: '密码',
  })
  password!: string;

  @Column({
    default: true,
    comment: '是否启用',
  })
  isActive!: boolean;

  @Column({
    default: false,
    comment: '是否为系统管理员',
  })
  isSystemAdmin!: boolean;

  @Column({
    nullable: true,
    comment: '手机号码',
  })
  phone?: string;

  @Column({
    nullable: true,
    comment: '重置密码令牌',
  })
  resetPasswordToken?: string;

  @Column({
    nullable: true,
    comment: '重置密码过期时间',
  })
  resetPasswordExpires?: Date;
}
