/* eslint-disable */
import 'reflect-metadata';
import { config } from 'dotenv';
import { AppDataSource } from '../src/config/database';
import { MembershipProductService } from '../src/services/membership-product.service';
import { PermissionService } from '../src/services/permission.service';

// 加载环境变量
config();

/**
 * 权限数据
 */
const permissionsData = [
  {
    name: '基础翻译',
    key: 'basic.translation',
    description: '基础文本翻译功能',
    group: '翻译功能',
    isActive: true,
  },
  {
    name: '高级翻译',
    key: 'premium.translation',
    description: '高级翻译功能，支持更多语言和更高精度',
    group: '翻译功能',
    isActive: true,
  },
  {
    name: '图片翻译',
    key: 'image.translation',
    description: '图片内容识别和翻译功能',
    group: '翻译功能',
    isActive: true,
  },
  {
    name: '批量翻译',
    key: 'batch.translation',
    description: '批量文本翻译功能',
    group: '翻译功能',
    isActive: true,
  },
  {
    name: 'API访问',
    key: 'api.access',
    description: '翻译API接口访问权限',
    group: 'API功能',
    isActive: true,
  },
  {
    name: '无限制使用',
    key: 'unlimited.usage',
    description: '无使用次数限制',
    group: '使用限制',
    isActive: true,
  },
  {
    name: '优先支持',
    key: 'priority.support',
    description: '优先技术支持服务',
    group: '客户服务',
    isActive: true,
  },
];

/**
 * 会员商品数据
 */
const membershipProductsData = [
  {
    name: '标准会员（1个月）',
    description: '标准会员一个月（30天）',
    price: 9900, // 59元，单位：分
    duration: 30, // 30天
    sort: 1,
    isActive: true,
    iconUrl: 'https://example.com/icons/standard-member.png',
    permissionKeys: [],
  },
  {
    name: '标准会员（3个月）',
    description: '标准会员三个月（90天）',
    price: 25900, // 99元，单位：分
    duration: 90, // 90天
    sort: 2,
    isActive: true,
    iconUrl: 'https://example.com/icons/premium-member.png',
    permissionKeys: [],
  },
  {
    name: '标准会员（1年）',
    description: '标准会员一年（365天）',
    price: 99900, // 999元，单位：分
    duration: 365, // 365天
    sort: 3,
    isActive: true,
    iconUrl: 'https://example.com/icons/yearly-member.png',
    permissionKeys: [],
  },
];

/**
 * 导入权限数据
 */
async function importPermissions(permissionService: PermissionService): Promise<Map<string, string>> {
  console.log('开始导入权限数据...');

  const permissionMap = new Map<string, string>();

  for (const permissionData of permissionsData) {
    // 检查权限是否已存在
    const existingPermission = await permissionService.getPermissionByKey(permissionData.key);

    if (existingPermission) {
      console.log(`权限已存在: ${permissionData.name} (${permissionData.key})`);
      permissionMap.set(permissionData.key, existingPermission.id);
    } else {
      const permission = await permissionService.create(permissionData);
      console.log(`创建权限: ${permission.name} (${permission.key})`);
      permissionMap.set(permissionData.key, permission.id);
    }
  }

  console.log(`权限数据导入完成，共处理 ${permissionsData.length} 个权限`);
  return permissionMap;
}

/**
 * 导入会员商品数据
 */
async function importMembershipProducts() {
  try {
    console.log('开始连接数据库...');
    await AppDataSource.initialize();
    console.log('数据库连接成功');

    const membershipProductService = new MembershipProductService();
    const permissionService = new PermissionService();

    // 检查是否已有数据
    const existingProducts = await membershipProductService.findAll();
    if (existingProducts.length > 0) {
      console.log(`发现已有 ${existingProducts.length} 个会员商品，是否继续导入？`);
      console.log('如需重新导入，请先清空 membership_products 和 membership_permissions 表');
      return;
    }

    // 先导入权限数据
    const permissionMap = await importPermissions(permissionService);

    console.log('开始导入会员商品数据...');

    // 批量创建会员商品
    const createdProducts = [];

    for (const productData of membershipProductsData) {
      const { permissionKeys, ...productInfo } = productData;

      // 获取权限ID列表
      const permissionIds = permissionKeys.map(key => {
        const permissionId = permissionMap.get(key);
        if (!permissionId) {
          throw new Error(`权限不存在: ${key}`);
        }
        return permissionId;
      });

      // 创建会员商品
      const product = await membershipProductService.createMembershipProduct({
        ...productInfo,
        permissionIds,
      });

      createdProducts.push(product);
      console.log(`创建会员商品: ${product.name} - ¥${(product.price / 100).toFixed(2)} - ${product.duration}天`);
    }

    console.log(`\n成功导入 ${createdProducts.length} 个会员商品：`);
    createdProducts.forEach((product, index) => {
      console.log(`${index + 1}. ${product.name}`);
      console.log(`   价格: ¥${(product.price / 100).toFixed(2)}`);
      console.log(`   时长: ${product.duration}天`);
      console.log(`   描述: ${product.description}`);
      console.log('');
    });

    console.log('会员商品数据导入完成！');
  } catch (error) {
    console.error('导入会员商品数据失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('数据库连接已关闭');
    }
  }
}

/**
 * 清空会员商品数据
 */
async function clearMembershipProducts() {
  try {
    console.log('开始连接数据库...');
    await AppDataSource.initialize();
    console.log('数据库连接成功');

    const membershipProductService = new MembershipProductService();
    const existingProducts = await membershipProductService.findAll();

    if (existingProducts.length === 0) {
      console.log('没有找到会员商品数据');
      return;
    }

    console.log(`找到 ${existingProducts.length} 个会员商品，开始清空...`);

    for (const product of existingProducts) {
      await membershipProductService.delete(product.id);
      console.log(`已删除: ${product.name}`);
    }

    console.log('会员商品数据清空完成！');
  } catch (error) {
    console.error('清空会员商品数据失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('数据库连接已关闭');
    }
  }
}

/**
 * 显示当前会员商品数据
 */
async function showMembershipProducts() {
  try {
    console.log('开始连接数据库...');
    await AppDataSource.initialize();
    console.log('数据库连接成功');

    const membershipProductService = new MembershipProductService();
    const products = await membershipProductService.findAll();

    if (products.length === 0) {
      console.log('没有找到会员商品数据');
      return;
    }

    console.log(`\n当前会员商品列表 (共 ${products.length} 个):`);
    console.log('='.repeat(60));

    for (const product of products) {
      const productDetail = await membershipProductService.getMembershipProductDetail(product.id);

      console.log(`${productDetail.name}`);
      console.log(`  ID: ${productDetail.id}`);
      console.log(`  价格: ¥${(productDetail.price / 100).toFixed(2)}`);
      console.log(`  时长: ${productDetail.duration}天`);
      console.log(`  状态: ${productDetail.isActive ? '启用' : '禁用'}`);
      console.log(`  排序: ${productDetail.sort}`);
      console.log(`  描述: ${productDetail.description || '无'}`);

      if (productDetail.permissions && productDetail.permissions.length > 0) {
        console.log(`  权限: ${productDetail.permissions.map((p: any) => p.name).join(', ')}`);
      }

      console.log('');
    }
  } catch (error) {
    console.error('查看会员商品数据失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('数据库连接已关闭');
    }
  }
}

// 根据命令行参数执行不同操作
const action = process.argv[2];

if (action === 'clear') {
  clearMembershipProducts();
} else if (action === 'show') {
  showMembershipProducts();
} else {
  importMembershipProducts();
}

// 使用说明
if (!action) {
  console.log('\n使用说明:');
  console.log('导入会员商品数据: npm run import-membership-products');
  console.log('清空会员商品数据: npm run import-membership-products clear');
  console.log('查看会员商品数据: npm run import-membership-products show');
}
