import { AppDataSource } from '../config/database';
import { BaseService } from './base.service';
import { TranslationRoute } from '../entities/TranslationRoute';
import { Account } from '../entities/Account';
import { VendorType } from '../entities/TranslationRoute';

// 支持的语言列表
const SUPPORTED_LANGUAGES = {
  [VendorType.BAIDU]: {
    auto: '自动检测',
    zh: '中文',
    en: '英语',
    jp: '日语',
    kor: '韩语',
    fra: '法语',
    spa: '西班牙语',
    th: '泰语',
    ara: '阿拉伯语',
    ru: '俄语',
    pt: '葡萄牙语',
    de: '德语',
    it: '意大利语',
    el: '希腊语',
    nl: '荷兰语',
    pl: '波兰语',
    bul: '保加利亚语',
    est: '爱沙尼亚语',
    dan: '丹麦语',
    fin: '芬兰语',
    cs: '捷克语',
    rom: '罗马尼亚语',
    slo: '斯洛文尼亚语',
    swe: '瑞典语',
    hu: '匈牙利语',
    cht: '繁体中文',
    vie: '越南语',
  },
  [VendorType.YOUDAO]: {
    auto: '自动检测',
    'zh-CHS': '中文',
    en: '英语',
    ja: '日语',
    ko: '韩语',
    fr: '法语',
    es: '西班牙语',
    pt: '葡萄牙语',
    it: '意大利语',
    ru: '俄语',
    vi: '越南语',
    de: '德语',
    ar: '阿拉伯语',
    id: '印尼语',
    af: '南非语',
    bs: '波斯尼亚语',
    bg: '保加利亚语',
    yue: '粤语',
    ca: '加泰隆语',
    hr: '克罗地亚语',
    cs: '捷克语',
    da: '丹麦语',
    nl: '荷兰语',
    et: '爱沙尼亚语',
    fj: '斐济语',
    fi: '芬兰语',
    el: '希腊语',
    ht: '海地克里奥尔语',
    he: '希伯来语',
    hi: '印地语',
    mww: '白苗语',
    hu: '匈牙利语',
    sw: '斯瓦希里语',
    tlh: '克林贡语',
    lv: '拉脱维亚语',
    lt: '立陶宛语',
    ms: '马来语',
    mt: '马耳他语',
    no: '挪威语',
    fa: '波斯语',
    pl: '波兰语',
    ro: '罗马尼亚语',
    'sr-Cyrl': '塞尔维亚语(西里尔文)',
    'sr-Latn': '塞尔维亚语(拉丁文)',
    sk: '斯洛伐克语',
    sl: '斯洛文尼亚语',
    sv: '瑞典语',
    ty: '塔希提语',
    th: '泰语',
    to: '汤加语',
    tr: '土耳其语',
    uk: '乌克兰语',
    ur: '乌尔都语',
    cy: '威尔士语',
    yua: '尤卡坦玛雅语',
    sq: '阿尔巴尼亚语',
    am: '阿姆哈拉语',
    hy: '亚美尼亚语',
    az: '阿塞拜疆语',
    bn: '孟加拉语',
    eu: '巴斯克语',
    be: '白俄罗斯语',
    ceb: '宿务语',
    co: '科西嘉语',
    eo: '世界语',
    tl: '菲律宾语',
    fy: '弗里西语',
    gl: '加利西亚语',
    ka: '格鲁吉亚语',
    gu: '古吉拉特语',
    ha: '豪萨语',
    haw: '夏威夷语',
    is: '冰岛语',
    ig: '伊博语',
    ga: '爱尔兰语',
    jw: '爪哇语',
    kn: '卡纳达语',
    kk: '哈萨克语',
    km: '高棉语',
    ku: '库尔德语',
    ky: '吉尔吉斯语',
    lo: '老挝语',
    la: '拉丁语',
    lb: '卢森堡语',
    mk: '马其顿语',
    mg: '马尔加什语',
    ml: '马拉雅拉姆语',
    mi: '毛利语',
    mr: '马拉地语',
    mn: '蒙古语',
    my: '缅甸语',
    ne: '尼泊尔语',
    ny: '齐切瓦语',
    ps: '普什图语',
    pa: '旁遮普语',
    sm: '萨摩亚语',
    gd: '苏格兰盖尔语',
    st: '塞索托语',
    sn: '修纳语',
    sd: '信德语',
    si: '僧伽罗语',
    so: '索马里语',
    su: '巽他语',
    tg: '塔吉克语',
    ta: '泰米尔语',
    te: '泰卢固语',
    uz: '乌兹别克语',
    xh: '科萨语',
    yi: '意第绪语',
    yo: '约鲁巴语',
    zu: '祖鲁语',
  },
  [VendorType.ALIBABA]: {
    zh: '中文',
    en: '英语',
    ja: '日语',
    ko: '韩语',
    fr: '法语',
    es: '西班牙语',
    it: '意大利语',
    de: '德语',
    tr: '土耳其语',
    ru: '俄语',
    pt: '葡萄牙语',
    vi: '越南语',
    id: '印尼语',
    th: '泰语',
    ms: '马来语',
    ar: '阿拉伯语',
    hi: '印地语',
  },
};

// 支持的供应商列表
export const SUPPORTED_VENDORS = [
  {
    type: VendorType.BAIDU,
    name: '百度翻译',
    description: '百度翻译开放平台',
    icon: 'https://fanyi-api.baidu.com/static/translation/img/logo/fanyi_logo_blue.png',
    website: 'https://fanyi-api.baidu.com/',
  },
  {
    type: VendorType.YOUDAO,
    name: '有道翻译',
    description: '有道智云翻译服务',
    icon: 'https://ai.youdao.com/favicon.ico',
    website: 'https://ai.youdao.com/',
  },
  {
    type: VendorType.ALIBABA,
    name: '阿里云翻译',
    description: '阿里云机器翻译服务',
    icon: 'https://img.alicdn.com/tfs/TB1_ZXuNcfpK1RjSZFOXXa6nFXa-32-32.ico',
    website: 'https://www.aliyun.com/product/ai/alimt',
  },
  // {
  //   type: VendorType.GOOGLE,
  //   name: '谷歌翻译',
  //   description: 'Google Cloud Translation API',
  //   icon: 'https://www.google.com/images/branding/googlelogo/1x/googlelogo_color_272x92dp.png',
  //   website: 'https://cloud.google.com/translate',
  // },
  // {
  //   type: VendorType.DEEPL,
  //   name: 'DeepL翻译',
  //   description: 'DeepL API翻译服务',
  //   icon: 'https://www.deepl.com/img/favicon/favicon_96.png',
  //   website: 'https://www.deepl.com/pro-api',
  // },
];

export class TranslationRouteService extends BaseService<TranslationRoute> {
  constructor() {
    super(AppDataSource.getRepository(TranslationRoute));
  }

  // 脱敏处理工具方法
  private maskApiSecret(apiSecret: string | undefined): string | undefined {
    if (!apiSecret) return undefined;
    if (apiSecret.length <= 8) return '****';
    return `${apiSecret.slice(0, 4)}****${apiSecret.slice(-4)}`;
  }

  // 处理单个路由的脱敏
  private processRouteMask(route: TranslationRoute): TranslationRoute {
    return {
      ...route,
      apiSecret: this.maskApiSecret(route.apiSecret),
    };
  }

  // 处理路由数组的脱敏
  private processRoutesMask(routes: TranslationRoute[]): TranslationRoute[] {
    return routes.map((route) => this.processRouteMask(route));
  }

  // 获取支持的语言列表
  async getSupportedLanguages(
    vendor: VendorType,
    type: 'source' | 'target' = 'source',
  ): Promise<{ [key: string]: string } | null> {
    const languages = SUPPORTED_LANGUAGES[vendor];
    if (!languages) {
      return null;
    }

    // 阿里云图片翻译的特殊处理：仅支持中/英文作为源语言
    if (vendor === VendorType.ALIBABA && type === 'source') {
      return {
        zh: '中文',
        en: '英语',
      };
    }

    // 如果是目标语言，去掉auto选项
    if (type === 'target') {
      const { auto, ...targetLanguages } = languages;
      return targetLanguages;
    }

    return languages;
  }

  async createRoute(data: Partial<TranslationRoute>, account: Account): Promise<TranslationRoute> {
    const route = this.repository.create({
      ...data,
      accountId: account.id,
    });
    const savedRoute = await this.repository.save(route);
    return this.processRouteMask(savedRoute);
  }

  async updateRoute(id: string, data: Partial<TranslationRoute>): Promise<boolean> {
    const result = await this.update(id, data);
    return result;
  }

  async getRoutesByAccount(accountId: string): Promise<TranslationRoute[]> {
    const routes = await this.repository.find({
      where: [{ accountId }, { perm: 2 }],
      order: { sort: 'ASC' },
    });
    return routes;
  }

  async toggleRouteStatus(id: string): Promise<boolean> {
    const route = await this.repository.findOne({ where: { id } });
    if (route === null) {
      // 若 route 为 null，可根据实际情况处理，这里简单返回 false
      return false;
    }
    return this.update(id, { isActive: !route.isActive });
  }

  async deleteRoute(id: string): Promise<void> {
    await this.repository.delete(id);
  }

  // 获取支持的供应商列表
  async getSupportedVendors(): Promise<typeof SUPPORTED_VENDORS> {
    return SUPPORTED_VENDORS;
  }
}
