import { Context } from 'koa';
import { ChatAccountService } from '../../services/chat-account.service';

interface CreateChatAccountBody {
  accountId?: string;
  nickname?: string;
  avatar?: string;
}

/**
 * 聊天账号控制器
 */
export default class ChatAccountController {
  private chatAccountService: ChatAccountService;

  constructor() {
    this.chatAccountService = new ChatAccountService();
  }

  /**
   * 创建聊天账号
   */
  public create = async (ctx: Context): Promise<void> => {
    try {
      const { user } = ctx.state;
      const body = ctx.request.body as CreateChatAccountBody;
      const account = await this.chatAccountService.create(body, user.id);

      ctx.body = {
        code: 200,
        data: account,
        message: '创建聊天账号成功',
      };
    } catch (error) {
      console.error('创建聊天账号失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '创建聊天账号失败',
      };
    }
  };

  /**
   * 更新聊天账号
   */
  public update = async (ctx: Context): Promise<void> => {
    try {
      const { id } = ctx.params;
      const body = ctx.request.body as CreateChatAccountBody;
      const account = await this.chatAccountService.update(id, body);

      ctx.body = {
        code: 200,
        data: account,
        message: '更新聊天账号成功',
      };
    } catch (error) {
      console.error('更新聊天账号失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '更新聊天账号失败',
      };
    }
  };

  /**
   * 删除聊天账号
   */
  public delete = async (ctx: Context): Promise<void> => {
    try {
      const { id } = ctx.params;
      await this.chatAccountService.delete(id);

      ctx.body = {
        code: 200,
        message: '删除聊天账号成功',
      };
    } catch (error) {
      console.error('删除聊天账号失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '删除聊天账号失败',
      };
    }
  };

  /**
   * 获取单个聊天账号
   */
  public findOne = async (ctx: Context): Promise<void> => {
    try {
      const { id } = ctx.params;
      const account = await this.chatAccountService.findOne(id);

      ctx.body = {
        code: 200,
        data: account,
        message: '获取聊天账号成功',
      };
    } catch (error) {
      console.error('获取聊天账号失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '获取聊天账号失败',
      };
    }
  };

  /**
   * 获取聊天账号列表
   */
  public findAll = async (ctx: Context): Promise<void> => {
    try {
      const { user } = ctx.state;
      const accounts = await this.chatAccountService.findAll(user.id);

      ctx.body = {
        code: 200,
        data: accounts,
        message: '获取聊天账号列表成功',
      };
    } catch (error) {
      console.error('获取聊天账号列表失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '获取聊天账号列表失败',
      };
    }
  };
}
