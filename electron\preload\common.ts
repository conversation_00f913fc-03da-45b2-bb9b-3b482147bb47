import { ipc<PERSON><PERSON><PERSON> } from "electron";
import { domReady, waitForElement, createTranslateButton } from './tools';
import { FingerprintConfig, TranslateConfig } from "../main/interface";

/**
 * 通用桥接类，封装跨平台通用功能：翻译初始化、DOM监听、指纹设置等
 */
export class CommonBridge {
    /**
     * 构造函数，初始化CommonBridge实例
     */
    constructor() {
        console.log('CommonBridge');
    }

    /**
     * 初始化输入框翻译功能
     * 支持Ctrl+T翻译当前输入内容，Ctrl+Z撤销翻译
     * @param inputSelector 输入框CSS选择器
     */
    public initTranslateInput(inputSelector: string): void {
        const inputHistory = new Map<string, string>();

        const setCaretToEnd = (el: HTMLElement) => {
            el.focus();
            const range = document.createRange();
            range.selectNodeContents(el);
            range.collapse(false);
            const sel = window.getSelection();
            sel.removeAllRanges();
            sel.addRange(range);
        };

        const handleTranslateInput = async (target: HTMLElement) => {
            inputHistory.set(target.id, target.innerHTML);
            const translateConfig = await this.getTranslateConfig();
            if (translateConfig.translateInput === false) {
                return;
            }
            await this.translateNode(target);
            target.dispatchEvent(new Event('input', { bubbles: true }));
            setCaretToEnd(target);
        };

        const handleUndoInput = (target: HTMLElement) => {
            const prev = inputHistory.get(target.id);
            if (prev !== undefined) {
                target.innerHTML = prev;
                target.dispatchEvent(new Event('input', { bubbles: true }));
                inputHistory.delete(target.id);
            }
        };

        document.addEventListener('keydown', async (e) => {
            const target = e.target as HTMLElement;
            if (e.ctrlKey && (e.key === 't' || e.key === 'T')) {
                if (target.matches(inputSelector)) {
                    e.preventDefault();
                    await handleTranslateInput(target);
                }
            } else if (e.ctrlKey && (e.key === 'z' || e.key === 'Z')) {
                if (target.matches(inputSelector)) {
                    e.preventDefault();
                    handleUndoInput(target);
                }
            }
        });
    }

    /**
     * 初始化聊天项翻译功能
     * 为所有匹配选择器的聊天项添加翻译按钮
     * @param chatItemSelector 聊天项容器CSS选择器
     * @param contentInnerSelector 聊天内容CSS选择器
     */
    public initTranslateChatItems(chatItemSelector: string, contentInnerSelector: string): void {
        const chatList = this.getChatItemNodeList(chatItemSelector);
        Array.from(chatList).forEach(node => {
            this.initTranslateChatItem(node, contentInnerSelector);
        });
    }

    /**
     * 为单个聊天项初始化翻译功能
     * 添加翻译按钮并绑定点击事件
     * @param node 聊天项DOM元素
     * @param contentInnerSelector 聊天内容CSS选择器
     */
    protected initTranslateChatItem(node: Element, contentInnerSelector: string): void {
        if (!node.hasAttribute('is-translated-inited')) {
            node.setAttribute('is-translated-inited', 'true');
            const contentInner = node.querySelector(contentInnerSelector);
            if (contentInner) {
                const translateButton = createTranslateButton({
                    initialText: '----- 开始翻译 -----',
                    loadingText: '----- 翻译中... -----',
                    errorText: '----- 翻译失败 -----',
                    retryText: '----- 重新翻译 -----',
                    onClick: async (buttonContainer) => {
                        const messageContent = buttonContainer.closest('.message-content');
                        if (!messageContent) return;
                        const existingTranslation = messageContent.querySelector('.translated-content');
                        if (existingTranslation) existingTranslation.remove();
                        const translateConfig = await this.getTranslateConfig();
                        const translatedContent = await this.translateNode(
                            contentInner.cloneNode(true) as HTMLElement);
                        translatedContent.classList.add('translated-content');
                        buttonContainer.parentNode?.insertBefore(translatedContent, buttonContainer.nextSibling);
                    }
                });
                contentInner.parentNode?.insertBefore(translateButton, contentInner.nextSibling);
            }
        }
    }

    /**
     * 设置浏览器指纹信息
     * 可伪造语言、分辨率、时区、平台等环境信息
     * @param fingerprintConfig 指纹配置对象
     */
    public async setFingerprint(fingerprintConfig: FingerprintConfig) {

        if (!fingerprintConfig || !fingerprintConfig.enabled) {
            return
        }
        // 语言
        if (fingerprintConfig.language) {
            Object.defineProperty(navigator, 'language', { get: () => fingerprintConfig.language });
            Object.defineProperty(navigator, 'languages', { get: () => [fingerprintConfig.language] });
        }
        // 分辨率
        if (fingerprintConfig.resolution) {
            const [width, height] = fingerprintConfig.resolution.split('x');
            Object.defineProperty(screen, 'width', { get: () => Number(width) });
            Object.defineProperty(screen, 'height', { get: () => Number(height) });
            Object.defineProperty(window, 'innerWidth', { get: () => Number(width) });
            Object.defineProperty(window, 'innerHeight', { get: () => Number(height) });
        }
        // 时区
        if (fingerprintConfig.timezone) {
            // 伪造时区
            // @ts-ignore
            Intl.DateTimeFormat = (function (orig) {
                return function () {
                    return { resolvedOptions: () => ({ timeZone: fingerprintConfig.timezone }) };
                };
            })(Intl.DateTimeFormat);
        }
        // platform/os
        if (fingerprintConfig.os) {
            Object.defineProperty(navigator, 'platform', { get: () => fingerprintConfig.os });
        }
        // browser
        if (fingerprintConfig.browser) {
            Object.defineProperty(navigator, 'appVersion', { get: () => fingerprintConfig.browser });
        }
        // WebRTC
        if (fingerprintConfig.webrtc === false) {
            if (window.RTCPeerConnection) {
                // @ts-ignore
                window.RTCPeerConnection = undefined;
            }
        }
        // Canvas
        if (fingerprintConfig.canvas === false) {
            const getContext = HTMLCanvasElement.prototype.getContext;
            HTMLCanvasElement.prototype.getContext = function (type: string, ...args: any[]) {
                if (type === '2d') return null;
                return getContext.call(this, type, ...args);
            };
        }
        // WebGL
        if (fingerprintConfig.webgl === false) {
            const getContext = HTMLCanvasElement.prototype.getContext;
            HTMLCanvasElement.prototype.getContext = function (type: string, ...args: any[]) {
                if (type === 'webgl' || type === 'webgl2') return null;
                return getContext.call(this, type, ...args);
            };
        }
    }

    /**
     * 启用文本翻译功能
     * 通过设置html属性标记翻译状态
     */
    public enableTextTranslate() {
        document.querySelector('html')?.setAttribute('data-text-enable-translate', 'true');
    }

    /**
     * 禁用文本翻译功能
     * 通过设置html属性标记翻译状态
     */
    public disableTextTranslate() {
        document.querySelector('html')?.setAttribute('data-text-enable-translate', 'false');
    }

    /**
     * 获取聊天项节点列表
     * @param selector 聊天项CSS选择器
     * @returns 匹配的DOM节点列表
     */
    public getChatItemNodeList(selector: string): NodeListOf<Element> {
        return document.querySelectorAll(selector);
    }

    /**
     * 获取节点下所有文本节点
     * 递归遍历DOM树，过滤链接标签和已翻译内容
     * @param node 起始DOM节点
     * @returns 文本节点数组
     */
    public getTextNodes(node: Node): Text[] {
        const textNodes: Text[] = [];

        // 递归遍历DOM树
        const walk = (node: Node) => {
            // 如果父节点是a标签，跳过整个分支
            if (node.parentElement?.tagName.toLowerCase() === 'a') {
                return;
            }

            if (node.nodeType === Node.TEXT_NODE) {
                // 如果是文本节点，添加到结果数组
                textNodes.push(node as Text);
            } else {
                // 如果是元素节点，遍历其子节点
                const children = node.childNodes;
                for (let i = 0; i < children.length; i++) {
                    // 跳过已翻译的内容和a标签
                    if (children[i] instanceof Element && (
                        (children[i] as Element).classList.contains('translated-content') ||
                        (children[i] as Element).tagName.toLowerCase() === 'a'
                    )) {
                        continue;
                    }
                    walk(children[i]);
                }
            }
        };

        walk(node);
        return textNodes;
    }

    /**
     * 翻译DOM节点中的文本内容
     * 递归遍历并翻译所有文本节点，保留原始HTML结构
     * @param node 待翻译的DOM元素
     * @param options 翻译选项（源语言、目标语言、翻译服务商）
     * @returns 翻译后的DOM元素
     */
    public async translateNode(node: HTMLElement): Promise<HTMLElement> {
        // 获取翻译配置
        const translateConfig = await this.getTranslateConfig();

        // 如果启用了文本翻译，则翻译文本内容
        if (translateConfig.translateChatRecord) {
            await this.translateTextInNode(node);
        }

        // 如果启用了图片翻译，则翻译图片内容
        if (translateConfig.translateImage) {
            await this.translateImagesInNode(node);
        }

        return node;
    }

    /**
     * 翻译DOM节点中的文本内容
     * @param node 待翻译的DOM元素
     * @param options 翻译选项
     */
    private async translateTextInNode(node: HTMLElement): Promise<void> {

        const translateConfig = await this.getTranslateConfig();

        const options = {
            from: translateConfig.chatRecordTranslateSource,
            to: translateConfig.chatRecordTranslateTarget,
            vendor: translateConfig.chatRecordTranslateLine
        }

        // 遍历所有文本节点
        const textNodes = this.getTextNodes(node);

        // 翻译每个文本节点
        for (const textNode of textNodes) {
            if (textNode.textContent && textNode.textContent.trim()) {
                try {
                    // 翻译文本内容
                    const translatedText = await ipcRenderer.invoke('translate-text', textNode.textContent, options);
                    // 更新节点内容
                    textNode.textContent = translatedText;
                } catch (error) {
                    console.error('Translation failed:', error);
                }
            }
        }
    }

    /**
     * 翻译DOM节点中的图片内容
     * @param node 待翻译的DOM元素
     * @param options 翻译选项
     */
    private async translateImagesInNode(node: HTMLElement): Promise<void> {

        // 获取翻译配置
        const translateConfig = await this.getTranslateConfig();
        const options = {
            from: translateConfig.imageTranslateSource,
            to: translateConfig.imageTranslateTarget,
            vendor: translateConfig.imageTranslateLine
        }

        // 获取所有图片元素
        const images = this.getImageNodes(node);

        // 如果没有图片，直接返回
        if (images.length === 0) {
            return;
        }

        console.log(`Found ${images.length} images to translate`);

        console.log(images.map(i => {
            return {
                src: i.src,
                width: i.width,
                height: i.height,
                offsetWidth: i.offsetWidth,
                offsetHeight: i.offsetHeight,
                clientWidth: i.clientWidth,
                clientHeight: i.clientHeight,
            }
        }))

        // 过滤出需要翻译的图片
        const imagesToTranslate = images.filter(img => {
            const width = img.width;
            const height = img.height;
            return width > 50 && height > 50;
        });

        console.log([imagesToTranslate])

        if (imagesToTranslate.length === 0) {
            console.log('No images need translation after filtering');
            return;
        }

        console.log(`${imagesToTranslate.length} images will be translated`);

        for (const img of imagesToTranslate) {
            // 将图片转换为base64
            const imageBase64 = await this.imageToBase64(img);
            if (imageBase64) {
                const [url, error] = await ipcRenderer.invoke('translate-image', imageBase64, options);
                if (!error) {
                    img.src = url;
                };
            }
        }

        console.log('All image translations completed');
    }

    /**
     * 获取节点下所有图片元素
     * @param node 起始DOM节点
     * @returns 图片元素数组
     */
    private getImageNodes(node: Node): HTMLImageElement[] {
        const images: HTMLImageElement[] = [];

        // 递归遍历DOM树
        const walk = (node: Node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
                const element = node as Element;

                // 如果是img标签，添加到结果数组
                if (element.tagName.toLowerCase() === 'img') {
                    images.push(element as HTMLImageElement);
                }

                // 遍历子节点
                const children = node.childNodes;
                for (let i = 0; i < children.length; i++) {
                    // 跳过已翻译的内容
                    if (children[i] instanceof Element &&
                        (children[i] as Element).classList.contains('translated-content')) {
                        continue;
                    }
                    walk(children[i]);
                }
            }
        };

        walk(node);
        return images;
    }

    /**
     * 将图片元素转换为base64格式
     * @param img 图片元素
     * @returns base64字符串或null
     */
    private async imageToBase64(img: HTMLImageElement): Promise<string | null> {
        try {
            // 创建canvas元素
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            if (!ctx) return null;

            // 等待图片加载完成
            await new Promise((resolve, reject) => {
                if (img.complete) {
                    resolve(void 0);
                } else {
                    img.onload = () => resolve(void 0);
                    img.onerror = reject;
                    // 设置超时
                    setTimeout(() => reject(new Error('Image load timeout')), 10000);
                }
            });

            // 设置canvas尺寸
            canvas.width = img.naturalWidth || img.width;
            canvas.height = img.naturalHeight || img.height;

            // 绘制图片到canvas
            ctx.drawImage(img, 0, 0);

            // 转换为base64
            return canvas.toDataURL('image/jpeg', 0.8);
        } catch (error) {
            console.error('Failed to convert image to base64:', error);
            return null;
        }
    }

    /**
     * 获取当前聊天ID
     * 从进程参数中解析@chatId=前缀的参数值
     * @returns 聊天ID字符串
     */
    public getChatId(): string {
        const argv = process.argv || [];
        for (const item of argv) {
            if (item.startsWith('@chatId=')) {
                return item.split('@chatId=')[1]
            }
        }
        return '';
    }

    /**
     * 获取翻译配置
     * 通过IPC调用主进程获取当前聊天的翻译设置
     * @returns 翻译配置对象
     */
    public async getTranslateConfig(): Promise<TranslateConfig> {
        const chatId = this.getChatId();
        return await ipcRenderer.invoke('get-translate-config', chatId)
    }

    /**
     * 获取指纹配置
     * 通过IPC调用主进程获取当前聊天的指纹设置
     * @returns 指纹配置对象
     */
    public async getFingerprintConfig(): Promise<FingerprintConfig> {
        const chatId = this.getChatId();
        return await ipcRenderer.invoke('get-fingerprint-config', chatId)
    }


    /**
     * 设置翻译状态
     * 根据当前翻译配置启用或禁用文本翻译功能
     */
    public async setTranslateState() {
        const translateConfig = await this.getTranslateConfig()
        if (translateConfig?.translateChatRecord || translateConfig?.translateImage) {
            this.enableTextTranslate();
        } else {
            this.disableTextTranslate()
        }
    }

    /**
     * 设置消息列表观察者
     * 监听指定DOM元素的子节点变化，触发回调函数
     * @param targetSelector 目标元素CSS选择器
     * @param callback 变化发生时的回调函数
     * @returns MutationObserver实例
     */
    public setupMessageObserver(targetSelector: string, callback: () => void): MutationObserver {
        const config = {
            attributes: false,
            childList: true,
            subtree: true,
            characterData: true
        };

        const observerCallback = (mutationsList: MutationRecord[]) => {
            for (let mutation of mutationsList) {
                if (mutation.type === 'childList') {
                    callback();
                }
            }
        };

        const observer = new MutationObserver(observerCallback);

        domReady().then(() => {
            waitForElement(targetSelector).then(() => {
                const targetNode = document.querySelector(targetSelector);
                if (targetNode) {
                    observer.observe(targetNode, config);
                    callback(); // 初始调用一次
                }
            });
        });

        return observer;
    }
}