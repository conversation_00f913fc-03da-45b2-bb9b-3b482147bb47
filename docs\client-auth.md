# 客户端认证模块 API 文档

## 概述

客户端认证模块提供用户账户的认证相关功能，包括注册、登录、个人资料管理、密码管理等。

**基础路径:** `/api/client/auth`

## 接口列表

### 1. 获取验证码

获取图片验证码，用于注册和登录时的安全验证。

- **URL:** `/api/client/auth/captcha`
- **方法:** `GET`
- **权限:** 公开接口
- **查询参数:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| type | string | 否 | 验证码类型，默认为图片验证码 |
| width | string | 否 | 验证码图片宽度 |
| height | string | 否 | 验证码图片高度 |

- **成功响应:**

```json
{
  "code": 200,
  "data": {
    "id": "captcha_123456",
    "dataUrl": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
  }
}
```

### 2. 发送注册验证码

发送邮箱验证码用于用户注册。

- **URL:** `/api/client/auth/send-register-code`
- **方法:** `POST`
- **权限:** 公开接口
- **请求体:**

```json
{
  "email": "<EMAIL>"
}
```

**请求参数说明:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| email | string | 是 | 用户邮箱地址 |

- **成功响应:**

```json
{
  "code": 200,
  "message": "验证码已发送至您的邮箱"
}
```

### 3. 用户注册

用户账户注册接口。

- **URL:** `/api/client/auth/register`
- **方法:** `POST`
- **权限:** 公开接口
- **请求体:**

```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "verificationCode": "123456"
}
```

**请求参数说明:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| email | string | 是 | 用户邮箱 |
| password | string | 是 | 用户密码 |
| verificationCode | string | 否 | 邮箱验证码 |

- **成功响应:**

```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "user_001",
      "name": "用户1",
      "email": "<EMAIL>",
      "createdAt": "2023-01-01T00:00:00.000Z"
    }
  }
}
```

### 4. 用户登录

用户账户登录接口，需要提供邮箱、密码和验证码。

- **URL:** `/api/client/auth/login`
- **方法:** `POST`
- **权限:** 公开接口
- **请求体:**

```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "captchaId": "captcha_123456",
  "captchaAnswer": "ABCD"
}
```

**请求参数说明:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| email | string | 是 | 用户邮箱 |
| password | string | 是 | 用户密码 |
| captchaId | string | 是 | 验证码ID |
| captchaAnswer | string | 是 | 验证码答案 |

- **成功响应:**

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "user_001",
      "name": "用户1",
      "email": "<EMAIL>",
      "createdAt": "2023-01-01T00:00:00.000Z"
    }
  }
}
```

### 5. 忘记密码

发送密码重置链接到用户邮箱。

- **URL:** `/api/client/auth/forgot-password`
- **方法:** `POST`
- **权限:** 公开接口
- **请求体:**

```json
{
  "email": "<EMAIL>"
}
```

**请求参数说明:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| email | string | 是 | 用户邮箱地址 |

- **成功响应:**

```json
{
  "code": 200,
  "message": "重置密码链接已发送至您的邮箱"
}
```

### 6. 重置密码

通过重置令牌设置新密码。

- **URL:** `/api/client/auth/reset-password`
- **方法:** `POST`
- **权限:** 公开接口
- **请求体:**

```json
{
  "token": "reset_token_123456",
  "password": "newPassword123"
}
```

**请求参数说明:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| token | string | 是 | 密码重置令牌 |
| password | string | 是 | 新密码 |

- **成功响应:**

```json
{
  "code": 200,
  "message": "密码重置成功"
}
```

## 需要认证的接口

以下接口需要在请求头中携带认证令牌：`Authorization: Bearer {token}`

### 7. 获取个人资料

获取当前登录用户的个人资料。

- **URL:** `/api/client/auth/profile`
- **方法:** `GET`
- **权限:** 需要用户认证

- **成功响应:**

```json
{
  "code": 200,
  "data": {
    "id": "user_001",
    "name": "用户1",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "tenantId": "tenant_001",
    "isActive": true,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### 8. 更新个人资料

更新当前登录用户的个人资料。

- **URL:** `/api/client/auth/profile`
- **方法:** `PUT`
- **权限:** 需要用户认证
- **请求体:**

```json
{
  "name": "新用户名",
  "phone": "13900139000"
}
```

**请求参数说明:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| name | string | 否 | 用户姓名 |
| phone | string | 否 | 手机号码 |

- **成功响应:**

```json
{
  "code": 200,
  "message": "个人资料更新成功",
  "data": {
    "id": "user_001",
    "name": "新用户名",
    "email": "<EMAIL>",
    "phone": "13900139000",
    "tenantId": "tenant_001",
    "isActive": true,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-02T00:00:00.000Z"
  }
}
```

### 9. 修改密码

修改当前登录用户的密码。

- **URL:** `/api/client/auth/password`
- **方法:** `PUT`
- **权限:** 需要用户认证
- **请求体:**

```json
{
  "oldPassword": "oldPassword123",
  "newPassword": "newPassword123"
}
```

**请求参数说明:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| oldPassword | string | 是 | 原密码 |
| newPassword | string | 是 | 新密码 |

- **成功响应:**

```json
{
  "code": 200,
  "message": "密码修改成功"
}
```

## 错误码说明

| 错误码 | 描述 |
|--------|------|
| 400 | 请求参数错误或验证失败 |
| 401 | 未授权或认证失败 |
| 403 | 权限不足 |
| 404 | 用户不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 用户登录需要验证码验证，验证码有效期为5分钟
2. 登录成功后返回的JWT令牌需要妥善保存
3. 密码重置令牌有效期为30分钟
4. 邮箱验证码有效期为10分钟
5. 密码长度至少6位，建议包含字母和数字
