import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import { BaseService } from './base.service';
import { MembershipProduct } from '../entities/MembershipProduct';
import { Permission } from '../entities/Permission';
import { MembershipPermission } from '../entities/MembershipPermission';

/**
 * 会员商品服务
 */
export class MembershipProductService extends BaseService<MembershipProduct> {
  private membershipPermissionRepository: Repository<MembershipPermission>;
  private permissionRepository: Repository<Permission>;

  constructor() {
    super(AppDataSource.getRepository(MembershipProduct));
    this.membershipPermissionRepository = AppDataSource.getRepository(MembershipPermission);
    this.permissionRepository = AppDataSource.getRepository(Permission);
  }

  /**
   * 创建会员商品
   */
  async createMembershipProduct(data: any): Promise<MembershipProduct> {
    const { permissionIds, ...productData } = data;

    // 创建会员商品
    const product = await this.create(productData);

    // 如果提供了权限ID列表，创建会员商品权限关联
    if (permissionIds && permissionIds.length > 0) {
      await this.updateProductPermissions(product.id, permissionIds);
    }

    return product;
  }

  /**
   * 更新会员商品
   */
  async updateMembershipProduct(id: string, data: any): Promise<boolean> {
    const { permissionIds, ...productData } = data;

    // 更新会员商品
    const updated = await this.update(id, productData);

    // 如果提供了权限ID列表，更新会员商品权限关联
    if (permissionIds) {
      await this.updateProductPermissions(id, permissionIds);
    }

    return updated;
  }

  /**
   * 更新会员商品权限关联
   */
  private async updateProductPermissions(
    productId: string,
    permissionIds: string[],
  ): Promise<void> {
    // 删除现有的会员商品权限关联
    await this.membershipPermissionRepository.delete({
      membershipProduct: { id: productId } as any,
    });

    // 创建新的会员商品权限关联
    for (const permissionId of permissionIds) {
      const membershipPermission = this.membershipPermissionRepository.create({
        membershipProduct: { id: productId },
        permission: { id: permissionId },
      });
      await this.membershipPermissionRepository.save(membershipPermission);
    }
  }

  /**
   * 获取会员商品详情（包含权限信息）
   */
  async getMembershipProductDetail(id: string): Promise<any> {
    const product = await this.repository.findOne({
      where: { id },
      relations: ['permissions', 'permissions.permission'],
    });

    if (!product) {
      return null;
    }

    // 提取权限信息
    const permissions = product.permissions.map((mp) => mp.permission);

    return {
      ...product,
      permissions,
    };
  }

  /**
   * 获取会员商品列表（前台使用，只返回启用的商品）
   */
  async getActiveMembershipProducts(): Promise<MembershipProduct[]> {
    return this.repository.find({
      where: { isActive: true },
      order: { sort: 'ASC' },
    });
  }
}
