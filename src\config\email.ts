import { envConfig } from '../utils/env.util';
envConfig();

export const SMTP_HOST = process.env.SMTP_HOST || 'smtp.example.com';
export const SMTP_PORT = process.env.SMTP_PORT || '587';
export const SMTP_USER = process.env.SMTP_USER || '<EMAIL>';
export const SMTP_PASS = process.env.SMTP_PASS || 'your-email-password';
export const SMTP_FROM = process.env.SMTP_FROM || '<EMAIL>';
export const SMTP_SECURE = process.env.SMTP_SECURE || 'true';
