import { IEventType } from "../constants/enum"

export const quitApp = () => {
    window.ipcRenderer?.invoke(IEventType.QuitApp)
}

export const toLoginWindow = () => {
    window.ipcRenderer?.invoke(IEventType.ToLoginWindow)
}

export const toMainWindow = () => {
    window.ipcRenderer?.invoke(IEventType.ToMainWindow)
}

export const toUpdateWindow = () => {
    window.ipcRenderer?.invoke(IEventType.ToUpdateWindow)
}

export const winMinimize = () => {
    window.ipcRenderer?.invoke(IEventType.WinMinimize)
}

export const winClose = () => {
    window.ipcRenderer?.invoke(IEventType.WinClose)
}

export const winMaximize = () => {
    return window.ipcRenderer?.invoke(IEventType.WinMaximize)
}
