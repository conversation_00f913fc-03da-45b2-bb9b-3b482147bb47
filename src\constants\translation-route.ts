import { TranslationType, VendorType } from '@/api/translation-route'

export interface ITranslationRouteConfig {
    type: TranslationType
    title: string
    description: string
    supportedVendors: VendorType[]
}

export const TRANSLATION_ROUTE_CONFIGS: ITranslationRouteConfig[] = [
    {
        type: TranslationType.TEXT,
        title: '文本翻译线路',
        description: '提供文本翻译的API，申请流程参考：API申请教程，可添加多个相同供应商的线路，作为线路轮换，避免单个API字符消耗用尽',
        supportedVendors: [VendorType.BAIDU, VendorType.YOUDAO]
    },
    {
        type: TranslationType.IMAGE,
        title: '图片翻译线路',
        description: '提供图片翻译的API，支持有道翻译和阿里云翻译，申请流程参考：API申请教程，可添加多个相同供应商的线路，作为线路轮换，避免单个API字符消耗用尽',
        supportedVendors: [VendorType.YOUDAO, VendorType.ALIBABA]
    },
    // {
    //     type: TranslationType.DOCUMENT,
    //     title: '文档翻译线路',
    //     description: '提供文档翻译的API，支持有道翻译和阿里云翻译，申请流程参考：API申请教程，可添加多个相同供应商的线路，作为线路轮换，避免单个API字符消耗用尽',
    //     supportedVendors: [VendorType.BAIDU, VendorType.YOUDAO, VendorType.ALIBABA]
    // },
    // {
    //     type: TranslationType.AUDIO,
    //     title: '音频翻译线路',
    //     description: '提供音频翻译的API，仅支持阿里翻译，申请流程参考：API申请教程，可添加多个相同供应商的线路，作为线路轮换，避免单个API字符消耗用尽',
    //     supportedVendors: [VendorType.BAIDU]
    // }
]

export const VENDOR_LABELS: Record<VendorType, string> = {
    // [VendorType.GOOGLE]: '谷歌翻译',
    [VendorType.BAIDU]: '百度翻译',
    [VendorType.YOUDAO]: '有道翻译',
    [VendorType.ALIBABA]: '阿里云翻译',
    // [VendorType.DEEPL]: 'DeepL'
}