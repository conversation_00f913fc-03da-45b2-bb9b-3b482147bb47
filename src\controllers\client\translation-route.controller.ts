import { TranslationRouteService } from '../../services/translation-route.service';
import { Account } from '../../entities/Account';
import { Context } from 'koa';
import { TranslationRoute } from '../../entities/TranslationRoute';

interface CreateTranslationRouteBody {
  type: TranslationRoute['type'];
  vendor: TranslationRoute['vendor'];
  apiKey: string;
  apiSecret?: string;
  isActive?: boolean;
  sort?: number;
  accountId: string;
}

export class TranslationRouteController {
  private service = new TranslationRouteService();

  /**
   * 创建翻译线路
   */
  async createRoute(ctx: Context) {
    try {
      const body = ctx.request.body as CreateTranslationRouteBody;
      const route = await this.service.createRoute(body, ctx.state.user as Account);
      ctx.body = {
        code: 200,
        data: route,
        message: '创建成功',
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '创建翻译线路失败',
      };
    }
  }

  /**
   * 更新翻译线路
   */
  async updateRoute(ctx: Context) {
    try {
      const body = ctx.request.body as Partial<CreateTranslationRouteBody>;
      const updated = await this.service.updateRoute(ctx.params.id, body);
      ctx.body = {
        code: 200,
        data: updated,
        message: '更新成功',
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '更新翻译线路失败',
      };
    }
  }

  /**
   * 获取翻译线路列表
   */
  async getRoutes(ctx: Context) {
    try {
      const routes = await this.service.getRoutesByAccount((ctx.state.user as Account).id);
      ctx.body = {
        code: 200,
        data: routes,
        message: '获取成功',
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取翻译线路失败',
      };
    }
  }

  /**
   * 切换线路状态
   */
  async toggleStatus(ctx: Context) {
    try {
      const updated = await this.service.toggleRouteStatus(ctx.params.id);
      ctx.body = {
        code: 200,
        data: updated,
        message: '状态切换成功',
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '切换线路状态失败',
      };
    }
  }

  /**
   * 删除翻译线路
   */
  async deleteRoute(ctx: Context) {
    try {
      const { id } = ctx.params;
      await this.service.deleteRoute(id);
      ctx.body = {
        code: 200,
        message: '删除成功',
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '删除翻译线路失败',
      };
    }
  }

  /**
   * 获取支持的供应商列表
   */
  async getSupportedVendors(ctx: Context) {
    try {
      const vendors = await this.service.getSupportedVendors();
      ctx.body = {
        code: 200,
        data: vendors,
        message: '获取成功',
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取供应商列表失败',
      };
    }
  }
}
