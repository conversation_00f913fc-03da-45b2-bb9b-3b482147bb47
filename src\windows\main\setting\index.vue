<template>
    <div class="w-full h-full pr-1 pb-1">
        <div class="flex bg-white rounded-lg w-full h-full">
            <div class="flex-shrink-0 w-[180px] p-2">
                <div v-for="(menu, index) in menus" :key="index"
                    class="rounded bg-[#f1f2f5] h-8 text-sm px-2 flex items-center leading-none cursor-pointer">
                    {{ menu.title }}
                </div>
            </div>
            <div class="flex-grow">
                <router-view></router-view>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';

const menus = ref([
    {
        title: '翻译线路设置',
        path: '/setting/translation-route'
    }
])
</script>