<template>
    <div class="h-full pb-1 pr-1">
        <div class="h-full flex flex-col bg-white rounded-lg pb-1">
            <!-- 翻译设置区域 -->
            <div class="p-4">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">翻译供应商:</label>
                        <el-select v-model="translateConfig.vendor" placeholder="请选择供应商" size="small"
                            style="width: 120px" @change="handleVendorChange">
                            <el-option v-for="vendor in textVendors" :key="vendor.value" :label="vendor.label"
                                :value="vendor.value" />
                        </el-select>
                    </div>
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">源语言:</label>
                        <el-select v-model="translateConfig.from" placeholder="请选择源语言" size="small"
                            style="width: 100px">
                            <el-option v-for="lang in sourceLanguages" :key="lang.value" :label="lang.label"
                                :value="lang.value" />
                        </el-select>
                    </div>
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">目标语言:</label>
                        <el-select v-model="translateConfig.to" placeholder="请选择目标语言" size="small" style="width: 100px">
                            <el-option v-for="lang in targetLanguages" :key="lang.value" :label="lang.label"
                                :value="lang.value" />
                        </el-select>
                    </div>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="flex-1 flex p-4 space-x-4 min-h-0">
                <!-- 原文区域 -->
                <div class="flex-1 bg-white rounded-lg shadow-sm border p-4 flex flex-col">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">原文</h3>
                    <div class="flex-1 flex flex-col">
                        <div class="flex justify-between items-center mb-3">
                            <span class="text-sm font-medium text-gray-700">请输入要翻译的文本:</span>
                            <div class="space-x-2">
                                <el-button size="small" @click="clearText">清空</el-button>
                                <el-button size="small" @click="pasteText">粘贴</el-button>
                            </div>
                        </div>
                        <el-input v-model="sourceText" type="textarea" placeholder="请输入要翻译的文本内容..." :rows="12"
                            resize="none" class="flex-1 h-full" maxlength="5000" show-word-limit />
                    </div>
                </div>

                <!-- 翻译按钮区域 -->
                <div class="w-12 flex flex-col items-center justify-center">
                    <el-button type="primary" :loading="translating"
                        :disabled="!sourceText.trim() || !translateConfig.vendor || !translateConfig.from || !translateConfig.to"
                        @click="handleTranslate" class="w-16 h-16 rounded-full">
                        <template v-if="!translating">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </template>
                    </el-button>
                    <span class="text-xs text-gray-500 mt-2 text-center">{{ translating ? '翻译中...' : '开始翻译' }}</span>
                </div>

                <!-- 翻译结果区域 -->
                <div class="flex-1 bg-white rounded-lg shadow-sm border p-4 flex flex-col">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">翻译结果</h3>
                    <div class="flex-1 border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <div v-if="!translationResult && !translating"
                            class="h-full flex items-center justify-center text-gray-400">
                            <div class="text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-300" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <p class="mt-2 text-sm">翻译结果将显示在这里</p>
                            </div>
                        </div>
                        <div v-else-if="translating" class="h-full flex items-center justify-center">
                            <div class="text-center">
                                <el-icon class="animate-spin text-2xl text-blue-500 mb-2">
                                    <Loading />
                                </el-icon>
                                <p class="text-sm text-gray-600">正在翻译文本内容...</p>
                            </div>
                        </div>
                        <div v-else class="h-full flex flex-col">
                            <div class="flex justify-between items-center mb-3">
                                <span class="text-sm font-medium text-gray-700">翻译内容:</span>
                                <div class="space-x-2">
                                    <el-button size="small" @click="copyResult">复制</el-button>
                                    <el-button size="small" @click="exportResult">导出</el-button>
                                </div>
                            </div>
                            <div class="flex-1 bg-white border rounded p-3 overflow-auto">
                                <pre class="whitespace-pre-wrap text-sm text-gray-800">{{ translationResult }}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { Loading } from '@element-plus/icons-vue'
import { translateAPI, getLanguagesAPI, VendorType, type ITranslateParams } from '@/api/translation'
import { $message } from '@/utils/message'

// 响应式数据
const sourceText = ref<string>('')
const translating = ref(false)
const translationResult = ref<string>('')

// 翻译配置
const translateConfig = reactive({
    vendor: VendorType.BAIDU as VendorType,
    from: 'auto',
    to: 'zh'
})

// 语言选项
const sourceLanguages = ref<Array<{ label: string; value: string }>>([])
const targetLanguages = ref<Array<{ label: string; value: string }>>([])

// 文本翻译供应商选项
const textVendors = computed(() => [
    { label: '百度翻译', value: VendorType.BAIDU },
    { label: '有道翻译', value: VendorType.YOUDAO }
])

// 清空文本
const clearText = () => {
    sourceText.value = ''
    translationResult.value = ''
}

// 粘贴文本
const pasteText = async () => {
    try {
        const text = await navigator.clipboard.readText()
        sourceText.value = text
        $message.success('粘贴成功')
    } catch (error) {
        console.error('粘贴失败:', error)
        $message.error('粘贴失败，请手动输入文本')
    }
}

// 供应商变更处理
const handleVendorChange = async () => {
    try {
        await loadLanguages()
    } catch (error) {
        console.error('加载语言列表失败:', error)
    }
}

// 加载语言列表
const loadLanguages = async () => {
    if (!translateConfig.vendor) return

    try {
        const [sourceRes, targetRes] = await Promise.all([
            getLanguagesAPI(translateConfig.vendor, 'source'),
            getLanguagesAPI(translateConfig.vendor, 'target')
        ])

        sourceLanguages.value = Object.entries(sourceRes.data).map(([value, label]) => ({
            label: label as string,
            value
        }))

        targetLanguages.value = Object.entries(targetRes.data).map(([value, label]) => ({
            label: label as string,
            value
        }))

        // 设置默认语言
        if (sourceLanguages.value.length > 0 && !translateConfig.from) {
            translateConfig.from = sourceLanguages.value.find(lang => lang.value === 'auto')?.value || sourceLanguages.value[0].value
        }
        if (targetLanguages.value.length > 0 && !translateConfig.to) {
            translateConfig.to = targetLanguages.value.find(lang => lang.value === 'zh')?.value || targetLanguages.value[0].value
        }
    } catch (error) {
        console.error('获取语言列表失败:', error)
        $message.error('获取语言列表失败')
    }
}

// 执行翻译
const handleTranslate = async () => {
    if (!sourceText.value.trim()) {
        $message.error('请输入要翻译的文本')
        return
    }

    if (!translateConfig.vendor || !translateConfig.from || !translateConfig.to) {
        $message.error('请完善翻译配置')
        return
    }

    translating.value = true
    translationResult.value = ''

    try {
        const params: ITranslateParams = {
            text: sourceText.value.trim(),
            from: translateConfig.from,
            to: translateConfig.to,
            vendor: translateConfig.vendor
        }

        const response = await translateAPI(params)
        translationResult.value = response.data.text
        $message.success('翻译完成')
    } catch (error: any) {
        console.error('翻译失败:', error)
        const errorMessage = error?.response?.message || error?.message || '翻译失败，请稍后重试'
        $message.error(errorMessage)
    } finally {
        translating.value = false
    }
}

// 复制结果
const copyResult = async () => {
    if (!translationResult.value) {
        $message.warning('没有可复制的内容')
        return
    }

    try {
        await navigator.clipboard.writeText(translationResult.value)
        $message.success('复制成功')
    } catch (error) {
        console.error('复制失败:', error)
        // 降级方案：使用传统方法复制
        try {
            const textArea = document.createElement('textarea')
            textArea.value = translationResult.value
            document.body.appendChild(textArea)
            textArea.select()
            document.execCommand('copy')
            document.body.removeChild(textArea)
            $message.success('复制成功')
        } catch (fallbackError) {
            $message.error('复制失败')
        }
    }
}

// 导出结果
const exportResult = () => {
    if (!translationResult.value) {
        $message.warning('没有可导出的内容')
        return
    }

    try {
        const content = `原文：\n${sourceText.value}\n\n翻译结果：\n${translationResult.value}\n\n翻译时间：${new Date().toLocaleString()}`
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
        const url = window.URL.createObjectURL(blob)

        const link = document.createElement('a')
        link.href = url
        link.download = `translation-${Date.now()}.txt`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        window.URL.revokeObjectURL(url)
        $message.success('导出成功')
    } catch (error) {
        console.error('导出失败:', error)
        $message.error('导出失败')
    }
}

// 组件挂载时初始化
onMounted(async () => {
    await loadLanguages()
})
</script>
<style lang="scss">
.el-textarea {
    textarea {
        height: 100%;
    }
}
</style>