<template>
    <svg class="w-[1em] h-[1em] align-[-0.15em] fill-current overflow-hidden" :style="style" aria-hidden="true"
        v-bind="$attrs">
        <use :href="`#icon-${name}`" />
    </svg>
</template>

<script setup lang="ts">
import { computed } from 'vue'
const props = defineProps({
    name: {
        type: String,
        required: true,
    },
    color: {
        type: String,
        default: ''
    },
    size: {
        type: String,
        default: '16px'
    }
});
const style = computed(() => {
    return {
        color: props.color,
        fontSize: props.size,
    }
})
</script>

<style scoped>
.svg-icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}
</style>