import { Rectangle } from "electron";
import { PlatformKeys, TranslationType, VendorType } from "../enums";

export interface SessionWindowsManagerOptions {
    id: string,
    url: string,
    platform: PlatformKeys,
    bounds: Rectangle;
    proxyConfig?: ProxyConfig;
}

export interface IAccountInfo {
    avatar?: string;
    accountId?: string;
    nickname?: string;
    isLogined: boolean;
    unreadCount: number;
}

export interface IChatWindowInfo {
    winId: string,
    isLoading: boolean;
    isLoadFail: boolean;
    isLoaded: boolean;
    isShow: boolean;
    isCreated: boolean;
    accountInfo: IAccountInfo
}

export interface TranslateConfig {
    // 聊天记录翻译
    translateChatRecord: boolean
    chatRecordTranslateLine: VendorType
    chatRecordTranslateSource: string
    chatRecordTranslateTarget: string

    // 输入框翻译
    translateInput: boolean
    inputTranslateLine: VendorType
    inputTranslateSource: string
    inputTranslateTarget: string

    // 图片翻译
    translateImage: boolean
    imageTranslateLine: VendorType
    imageTranslateSource: string
    imageTranslateTarget: string

    // 语音翻译
    translateAudio: boolean
    audioTranslateLine: VendorType
    audioTranslateSource: string
    audioTranslateTarget: string

}

export interface ProxyConfig {
    enabled: boolean,
    protocol: string,
    host: string,
    port: string,
    auth: boolean,
    username: string,
    password: string
}

export interface FingerprintConfig {
    enabled: boolean
    os: string
    browser: string
    resolution: string
    language: string
    timezone: string
    userAgent: string
    cookie: string
    webrtc: boolean
    canvas: boolean
    webgl: boolean
}

export interface TranslateBaseOptions {
    from: string
    to: string
    vendor: VendorType
}

export interface ITranslationRoute {
    id: string
    type: TranslationType
    vendor: VendorType
    apiKey: string
    apiSecret?: string
    isActive: boolean
    sort: number
    accountId: string
    createdAt: string
    updatedAt: string
    perm: 1 | 2, // 线路使用权限：1 私有权限 2 公共权限
}

export interface ITranslateTextParams {
    text: string
    from: string
    to: string
    vendor?: VendorType
}

export interface ITranslateImageParams {
    imageBase64: string
    from: string
    to: string
    vendor?: VendorType
}