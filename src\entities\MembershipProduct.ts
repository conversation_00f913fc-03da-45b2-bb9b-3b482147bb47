import { Entity, Column, OneToMany } from 'typeorm';
import { BaseEntity } from './BaseEntity';
import { MembershipPermission } from './MembershipPermission';

/**
 * 会员商品实体
 */
@Entity('membership_products')
export class MembershipProduct extends BaseEntity {
  @Column({
    length: 100,
    comment: '会员商品名称',
  })
  name!: string;

  @Column({
    comment: '会员商品描述',
    type: 'text',
    nullable: true,
  })
  description?: string;

  @Column({
    comment: '会员商品价格(分)',
    type: 'int',
  })
  price!: number;

  @Column({
    comment: '会员时长(天)',
    type: 'int',
  })
  duration!: number;

  @Column({
    comment: '商品排序',
    type: 'int',
    default: 0,
  })
  sort!: number;

  @Column({
    comment: '是否启用',
    default: true,
  })
  isActive!: boolean;

  @Column({
    comment: '会员图标URL',
    nullable: true,
  })
  iconUrl?: string;

  @OneToMany(
    () => MembershipPermission,
    (membershipPermission: MembershipPermission) => membershipPermission.membershipProduct,
  )
  permissions!: MembershipPermission[];
}
