import captcha from 'trek-captcha';
import { RedisService } from './redis.service';

export class CaptchaService {
  private redisService: RedisService;
  private readonly CAPTCHA_PREFIX = 'captcha:';
  private readonly CAPTCHA_EXPIRE_TIME = 300; // 5分钟过期

  constructor() {
    this.redisService = new RedisService();
  }

  /**
   * 生成验证码
   */
  async generateCaptcha(): Promise<{ id: string; dataUrl: string }> {
    const { token, buffer } = await captcha({ size: 4, style: -1 }); // style: -1 表示纯数字

    // 生成唯一ID
    const id = Math.random().toString(36).substring(2, 15);

    // 将验证码答案存储到Redis
    await this.redisService.set(`${this.CAPTCHA_PREFIX}${id}`, token, this.CAPTCHA_EXPIRE_TIME);

    // 将buffer转换为DataURL
    const dataUrl = `data:image/gif;base64,${buffer.toString('base64')}`;

    return { id, dataUrl };
  }

  /**
   * 验证验证码
   */
  async verifyCaptcha(id: string, answer: string): Promise<boolean> {
    const key = `${this.CAPTCHA_PREFIX}${id}`;
    const correctAnswer = await this.redisService.get(key);

    if (!correctAnswer) {
      return false;
    }

    // 验证完成后删除验证码
    await this.redisService.del(key);

    return correctAnswer.toLowerCase() === answer.toLowerCase();
  }
}
