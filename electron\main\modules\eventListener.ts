import { BrowserWindow, ipcMain, app, Rectangle } from "electron";
import { coreWindowsManager, chatWindowsManager } from '../windows/index'
import { PlatformKeys, IEventType, VendorType } from "../enums";
import { FingerprintConfig, ProxyConfig, TranslateConfig, TranslateBaseOptions, ITranslationRoute } from "../interface";
import { translateText, translateImage } from "../services/translate/index";
import { storeManager } from './store';

export async function eventListener() {
    // 退出应用
    ipcMain.handle(IEventType.QuitApp, () => {
        app.quit()
        process.exit(0)
    })

    // 去登录页
    ipcMain.handle(IEventType.ToLoginWindow, () => {
        coreWindowsManager.closeAllWindows();
        coreWindowsManager.createLoginWindow()
    })

    // 去主页
    ipcMain.handle(IEventType.ToMainWindow, () => {
        coreWindowsManager.closeAllWindows();
        coreWindowsManager.createMainWindow()
    })

    // 去更新页
    ipcMain.handle(IEventType.ToUpdateWindow, () => {
        coreWindowsManager.closeAllWindows();
        coreWindowsManager.createUpdateWindow()
    })

    // 窗口最小化
    ipcMain.handle(IEventType.WinMinimize, () => {
        BrowserWindow.getFocusedWindow().minimize();
    })

    // 关闭窗口
    ipcMain.handle(IEventType.WinClose, () => {
        BrowserWindow.getFocusedWindow().close();
    })

    // 窗口最大化
    ipcMain.handle(IEventType.WinMaximize, () => {
        const win = BrowserWindow.getFocusedWindow();
        if (win.isMaximized()) {
            win.unmaximize();
        } else {
            win.maximize()
        }
    })

    // 打开第三方聊天窗口
    ipcMain.handle(IEventType.CreateChatWindow, (e, chatId: string, platform: PlatformKeys, bounds: Rectangle) => {
        chatWindowsManager.createChatWindow(chatId, platform, bounds);
    })

    // 设置第三方聊天窗口位置
    ipcMain.handle(IEventType.SetChatWindowBounds, (e, bounds) => {
        chatWindowsManager.setBounds(bounds);
    })

    // 显示第三方聊天窗口
    ipcMain.handle(IEventType.ShowChatWindow, (e, chatId?: string) => {
        chatWindowsManager.show(chatId);
    })

    // 隐藏第三方聊天窗口
    ipcMain.handle(IEventType.HideChatWindow, () => {
        chatWindowsManager.hide();
    })

    // 关闭第三方聊天窗口
    ipcMain.handle(IEventType.CloseChatWindow, (e, chatId: string) => {
        chatWindowsManager.close(chatId);
    })

    // 获取窗口状态
    ipcMain.handle(IEventType.GetChatWindowInfo, (e, chatId: string) => chatWindowsManager.getChatWindowInfo(chatId))

    // 获取窗口状态（所有）
    ipcMain.handle(IEventType.GetChatWindowInfoList, (e) => chatWindowsManager.getChatWindowInfoList())

    // 刷新窗口
    ipcMain.handle(IEventType.ReloadChatWindow, (e, chatId: string) => chatWindowsManager.reload(chatId))

    // Token相关事件处理
    ipcMain.handle(IEventType.GetToken, () => {
        return storeManager.getToken();
    });

    ipcMain.handle(IEventType.SetToken, (_event, token: string) => {
        storeManager.setToken(token);
    });

    ipcMain.handle(IEventType.RemoveToken, () => {
        storeManager.removeToken();
    });

    // 处理翻译配置的存储
    ipcMain.handle(IEventType.SetTranslateConfig, (event, chatId: string, config: TranslateConfig) => {
        chatWindowsManager.setTranslateConfig(chatId, config);
    });

    // 处理翻译配置的读取
    ipcMain.handle(IEventType.GetTranslateConfig, (event, chatId: string) => {
        return chatWindowsManager.getTranslateConfig(chatId);
    });

    // 处理翻译配置的存储
    ipcMain.handle(IEventType.SetTranslateRoutes, (event, routes: ITranslationRoute[]) => {
        storeManager.setTranslateRoutes(routes);
    });

    // 处理翻译配置的读取
    ipcMain.handle(IEventType.GetTranslateRoutes, (event, vendor: VendorType) => {
        return storeManager.getTranslateRoutes();
    });

    // 处理代理配置的存储
    ipcMain.handle(IEventType.SetProxyConfig, (event, chatId: string, config: ProxyConfig) => {
        chatWindowsManager.setProxyConfig(chatId, config);
    });

    // 处理代理配置的读取
    ipcMain.handle(IEventType.GetProxyConfig, (event, chatId: string) => {
        return chatWindowsManager.getProxyConfig(chatId);
    });

    // 处理指纹浏览器配置的存储
    ipcMain.handle(IEventType.SetFingerprintConfig, (event, chatId: string, config: FingerprintConfig) => {
        chatWindowsManager.setFingerprintConfig(chatId, config);
    });

    // 处理指纹浏览器的读取
    ipcMain.handle(IEventType.GetFingerprintConfig, (event, chatId: string) => {
        return chatWindowsManager.getFingerprintConfig(chatId);
    });

    // 文字翻译
    ipcMain.handle(IEventType.TranslateText, async (event, text: string, translateConfig: TranslateBaseOptions) => {
        return await translateText({ text, ...translateConfig })
    })

    // 图片翻译
    ipcMain.handle(IEventType.TranslateImage, async (event, imageBase64: string, translateConfig: TranslateBaseOptions) => {
        return await translateImage({ imageBase64, ...translateConfig })
    })
}