export interface PaginationParams {
  page?: number;
  pageSize?: number;
  order?: 'ASC' | 'DESC';
  orderBy?: string;
}

export interface QueryParams extends PaginationParams {
  [key: string]: any;
}

export interface ResponseResult<T = any> {
  code: number;
  message: string;
  data?: T;
}

export interface PaginatedResult<T = any> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 别名，保持向后兼容性
export type PaginationOptions = PaginationParams;
export type PaginationResult<T = any> = PaginatedResult<T>;
