import Router from '@koa/router';
import { AdminMembershipProductController } from '../../controllers/admin/membership-product.controller';
import { AdminPermissionController } from '../../controllers/admin/permission.controller';
import { requireLogin } from '../../middleware/login-required.middleware';

const router = new Router({
  prefix: '/api/admin',
});

// 创建控制器实例
const membershipProductController = new AdminMembershipProductController();
const permissionController = new AdminPermissionController();

// 会员商品管理路由
router.get('/membership/products', requireLogin, membershipProductController.getList);
router.get('/membership/products/:id', requireLogin, membershipProductController.getDetail);
router.post('/membership/products', requireLogin, membershipProductController.create);
router.put('/membership/products/:id', requireLogin, membershipProductController.update);
router.delete('/membership/products/:id', requireLogin, membershipProductController.delete);

// 权限管理路由
router.get('/permissions', requireLogin, permissionController.getList);
router.get('/permissions/group', requireLogin, permissionController.getGroupList);
router.get('/permissions/:id', requireLogin, permissionController.getDetail);
router.post('/permissions', requireLogin, permissionController.create);
router.put('/permissions/:id', requireLogin, permissionController.update);
router.delete('/permissions/:id', requireLogin, permissionController.delete);

export default router;
