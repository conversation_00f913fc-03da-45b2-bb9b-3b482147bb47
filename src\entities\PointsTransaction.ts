import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Client } from './Client';

export enum PointsTransactionType {
  RECHARGE = 'recharge', // 充值
  CONSUME = 'consume', // 消费
  REFUND = 'refund', // 退还
  BONUS = 'bonus', // 赠送
  ADMIN_ADJUST = 'admin_adjust', // 管理员调整
}

export enum PointsTransactionReferenceType {
  ORDER = 'order', // 订单
  TRANSLATION = 'translation', // 翻译
  ADMIN = 'admin', // 管理员操作
}

/**
 * 积分流水实体
 */
@Entity('points_transactions')
export class PointsTransaction {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @CreateDateColumn({ type: 'datetime' })
  createdAt: Date;

  @Column({ name: 'client_id', comment: '用户ID' })
  clientId: string;

  @ManyToOne(() => Client)
  @JoinColumn({ name: 'client_id' })
  client: Client;

  @Column({
    type: 'enum',
    enum: PointsTransactionType,
    comment: '交易类型',
  })
  type: PointsTransactionType;

  @Column({ type: 'int', comment: '积分数量（正负）' })
  amount: number;

  @Column({ type: 'int', comment: '交易后余额' })
  balance: number;

  @Column({ length: 200, comment: '描述' })
  description: string;

  @Column({
    type: 'enum',
    enum: PointsTransactionReferenceType,
    nullable: true,
    comment: '关联类型',
  })
  referenceType?: PointsTransactionReferenceType;

  @Column({ length: 100, nullable: true, comment: '关联ID' })
  referenceId?: string;

  @Column({ type: 'json', nullable: true, comment: '扩展数据' })
  metadata?: Record<string, any>;

  /**
   * 是否为收入
   */
  get isIncome(): boolean {
    return this.amount > 0;
  }

  /**
   * 是否为支出
   */
  get isExpense(): boolean {
    return this.amount < 0;
  }
}
