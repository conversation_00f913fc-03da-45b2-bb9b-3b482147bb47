import { PRELOAD } from "../../config/config";
export default {
  title: "Main window",
  width: 1500,
  height: 900,
  minWidth: 500,
  minHeight: 500,
  // 创建无边框窗口
  show: true,
  transparent: true,//透明窗口
  backgroundColor: '#00000000',//窗口底色为透明色
  frame: false,//是否无边框
  webPreferences: {
    preload: PRELOAD,
    // Warning: Enable nodeIntegration and disable contextIsolation is not secure in production
    // Consider using contextBridge.exposeInMainWorld
    // Read more on https://www.electronjs.org/docs/latest/tutorial/context-isolation
    nodeIntegration: true,
  },
}