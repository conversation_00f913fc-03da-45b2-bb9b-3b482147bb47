# 百度文档翻译功能实现文档

## 🎯 功能概述

本文档详细说明了如何在现有的翻译系统中接入百度文档翻译功能。百度文档翻译结合高还原度的文档解析和机器翻译技术，提供多格式、多语种、高质量的文档翻译服务。

## 📋 API文档参考

- **官方文档**: https://cloud.baidu.com/doc/MT/s/Xky9x5xub
- **创建任务接口**: `https://aip.baidubce.com/rpc/2.0/mt/v2/doc-translation/create`
- **查询任务接口**: `https://aip.baidubce.com/rpc/2.0/mt/v2/doc-translation/query`
- **请求方式**: POST (application/json)
- **鉴权方式**: Access Token

## 🔧 技术实现

### 1. 后端服务实现

#### 1.1 数据模型扩展

在 `src/entities/TranslationRoute.ts` 中添加了文档翻译类型：

```typescript
export enum TranslationType {
  TEXT = 'text',
  AUDIO = 'audio',
  VIDEO = 'video',
  IMAGE = 'image',
  DOCUMENT = 'document', // 新增
}
```

#### 1.2 百度翻译服务扩展

在 `src/services/baidu-translation.service.ts` 中添加了以下功能：

```typescript
// 新增接口定义
interface BaiduDocTranslationCreateResult {
  result?: {
    id: string; // 文档翻译任务ID
  };
  log_id: number;
  error_code?: number;
  error_msg?: string;
}

interface BaiduDocTranslationQueryResult {
  result?: {
    data: {
      id: string;
      from: string;
      to: string;
      domain?: string;
      input: {
        format: string;
        filename: string;
        size: number;
        character_count?: number;
      };
      output?: {
        files: Array<{
          format: string;
          filename: string;
          size?: number;
          url?: string;
        }>;
      };
      status: 'NotStarted' | 'Running' | 'Succeeded' | 'Failed' | 'Expired';
      reason: string;
      created_at: number;
      updated_at: number;
      expired_at: number;
    };
  };
  log_id: number;
  error_code?: number;
  error_msg?: string;
}

// 核心方法
- getAvailableBaiduDocRoutes(): 获取可用的百度文档翻译线路
- createDocumentTranslation(): 创建文档翻译任务
- queryDocumentTranslation(): 查询文档翻译任务状态
- smartTranslateDocument(): 智能文档翻译（支持线路切换）
```

#### 1.3 支持的文档格式

| 输入格式 | 支持的输出格式 | 默认输出格式 |
|----------|----------------|--------------|
| doc | docx, pdf | docx |
| docx | docx, pdf | docx |
| pdf | docx, pdf | docx |
| txt | txt | txt |
| html | html | html |
| xml | xml | xml |
| ppt | pptx | pptx |
| pptx | pptx | pptx |
| xls | xlsx | xlsx |
| xlsx | xlsx | xlsx |

#### 1.4 控制器实现

在 `src/controllers/client/translation.controller.ts` 中添加了：

```typescript
// 接口定义
interface TranslateDocumentRequest {
  documentBase64: string;
  from: string;
  to: string;
  format: string;
  filename: string;
  vendor?: VendorType;
  domain?: string;
  outputFormats?: string[];
  filenamePrefix?: string;
}

interface QueryDocumentTranslationRequest {
  taskId: string;
  vendor?: VendorType;
}

// 核心方法
- translateDocument(): 创建文档翻译任务
- queryDocumentTranslation(): 查询文档翻译任务状态
```

#### 1.5 路由配置

在 `src/routes/client/translation.routes.ts` 中添加了新的路由：

```typescript
router
  .post('/translate-document', (ctx) => new TranslationController().translateDocument(ctx))
  .post('/query-document', (ctx) => new TranslationController().queryDocumentTranslation(ctx))
```

### 2. 前端API实现

#### 2.1 接口定义

在前端API文件中添加了文档翻译相关的接口：

```typescript
export interface ITranslateDocumentParams {
    documentBase64: string
    from: string
    to: string
    format: string
    filename: string
    vendor?: VendorType
    domain?: string
    outputFormats?: string[]
    filenamePrefix?: string
}

export interface ITranslateDocumentResponse {
    taskId: string
    message: string
}

export interface IQueryDocumentParams {
    taskId: string
    vendor?: VendorType
}

export interface IDocumentTranslationStatus {
    id: string
    from: string
    to: string
    domain?: string
    input: {
        format: string
        filename: string
        size: number
        character_count?: number
    }
    output?: {
        files: Array<{
            format: string
            filename: string
            size?: number
            url?: string
        }>
    }
    status: 'NotStarted' | 'Running' | 'Succeeded' | 'Failed' | 'Expired'
    reason: string
    created_at: number
    updated_at: number
    expired_at: number
}

export interface IQueryDocumentResponse {
    data: IDocumentTranslationStatus
}
```

#### 2.2 API函数

```typescript
// 执行文档翻译
export function translateDocumentAPI(data: ITranslateDocumentParams) {
    return request<ITranslateDocumentResponse>({
        url: '/client/translation/translate-document',
        method: 'POST',
        data,
        timeout: 60000 // 60秒超时
    })
}

// 查询文档翻译状态
export function queryDocumentTranslationAPI(data: IQueryDocumentParams) {
    return request<IQueryDocumentResponse>({
        url: '/client/translation/query-document',
        method: 'POST',
        data,
        timeout: 30000 // 30秒超时
    })
}
```

## 🔄 API调用流程

### 1. 创建文档翻译任务

```http
POST https://aip.baidubce.com/rpc/2.0/mt/v2/doc-translation/create?access_token={ACCESS_TOKEN}
Content-Type: application/json;charset=utf-8

{
    "from": "zh",
    "to": "en",
    "input": {
        "content": "base64_encoded_document",
        "format": "docx",
        "filename": "document.docx"
    },
    "domain": "general",
    "output": {
        "formats": ["docx", "pdf"],
        "filename_prefix": "translated_"
    }
}
```

### 2. 查询翻译任务状态

```http
POST https://aip.baidubce.com/rpc/2.0/mt/v2/doc-translation/query?access_token={ACCESS_TOKEN}
Content-Type: application/json;charset=utf-8

{
    "id": "task_id_here"
}
```

### 3. 响应格式

#### 创建任务响应

```json
{
    "result": {
        "id": "doc_trans_task_12345"
    },
    "log_id": 1234567890
}
```

#### 查询状态响应

```json
{
    "result": {
        "data": {
            "id": "doc_trans_task_12345",
            "from": "zh",
            "to": "en",
            "domain": "general",
            "input": {
                "format": "docx",
                "filename": "document.docx",
                "size": 1048576,
                "character_count": 5000
            },
            "output": {
                "files": [
                    {
                        "format": "docx",
                        "filename": "translated_document.docx",
                        "size": 1200000,
                        "url": "https://example.com/download/translated_document.docx"
                    }
                ]
            },
            "status": "Succeeded",
            "reason": "翻译完成",
            "created_at": 1640995200,
            "updated_at": 1640995800,
            "expired_at": 1641081600
        }
    },
    "log_id": 1234567890
}
```

## 🎨 用户界面设计

### 文档翻译工作流程

```
┌─────────────────────────────────────────┐
│ 1. 选择文档文件                          │
├─────────────────────────────────────────┤
│ 2. 设置翻译参数                          │
│    - 源语言/目标语言                     │
│    - 垂直领域（可选）                    │
│    - 输出格式（可选）                    │
│    - 文件名前缀（可选）                  │
├─────────────────────────────────────────┤
│ 3. 创建翻译任务                          │
│    [创建任务] → 返回任务ID               │
├─────────────────────────────────────────┤
│ 4. 查询翻译状态                          │
│    [查询状态] [自动查询]                 │
│    状态：NotStarted/Running/Succeeded    │
├─────────────────────────────────────────┤
│ 5. 下载翻译结果                          │
│    [下载文件] 翻译完成后可下载           │
└─────────────────────────────────────────┘
```

## 📊 功能特性对比

| 功能特性 | 文本翻译 | 图片翻译 | 文档翻译 |
|----------|----------|----------|----------|
| 处理方式 | 同步 | 同步 | 异步 |
| 支持格式 | 纯文本 | JPG/PNG/WebP | DOC/PDF/TXT等 |
| 返回结果 | 翻译文本 | 图片/文本 | 下载链接 |
| 处理时间 | 秒级 | 秒级 | 分钟级 |
| 文件大小限制 | 无 | 4MB | 50MB |
| 批量处理 | ❌ | ❌ | ❌ |
| 格式保持 | ❌ | ✅ | ✅ |

## 🚀 部署配置

### 1. 数据库配置

在 `translation_routes` 表中添加百度文档翻译线路：

```sql
INSERT INTO translation_routes (
    account_id,
    vendor,
    type,
    api_key,
    api_secret,
    is_active,
    name,
    description
) VALUES (
    'user_account_id',
    'baidu',
    'document',
    'your_baidu_api_key',
    'your_baidu_secret_key',
    true,
    '百度文档翻译',
    '百度AI文档翻译服务'
);
```

### 2. 环境配置

确保服务器有足够的存储空间处理文档文件：

```env
# 文档翻译配置
MAX_DOCUMENT_SIZE=50MB
DOCUMENT_UPLOAD_TIMEOUT=60000
TRANSLATION_QUERY_INTERVAL=5000
```

## 🧪 测试验证

### 1. 功能测试

使用提供的测试页面 `test-baidu-document-translation.html` 进行完整的功能测试。

### 2. 测试用例

推荐的测试场景：

1. **基础翻译**: 中文Word文档→英文
2. **多格式**: 测试PDF、PPT、Excel等格式
3. **大文件**: 测试接近50MB的文档
4. **异步处理**: 验证任务状态查询机制
5. **错误处理**: 无效格式、网络错误等
6. **并发测试**: 多个任务同时处理

### 3. 性能测试

- 文档上传速度
- 翻译处理时间
- 查询响应时间
- 下载速度

## 🔍 错误处理

### 常见错误码

| 错误码 | 错误信息 | 解决方法 |
|--------|----------|----------|
| 0 | Success | 成功 |
| 52001 | TIMEOUT | 请求超时，重试 |
| 52002 | SYSTEM ERROR | 系统错误，重试 |
| 54000 | PARAM_ERROR | 检查参数 |
| 69001 | document fail | 检查文档格式 |
| 69002 | document size limit 50M | 压缩文档大小 |
| 69003 | unsupported format | 使用支持的格式 |

### 错误处理策略

1. **文档验证**: 格式、大小检查
2. **任务监控**: 状态轮询机制
3. **超时处理**: 任务过期自动清理
4. **重试机制**: 失败任务自动重试
5. **用户通知**: 实时状态更新

## 📈 性能优化

### 1. 文档处理优化

- 文档大小限制：最大50MB
- 格式预检查：上传前验证
- 压缩优化：自动压缩大文档
- 分片上传：大文件分片处理

### 2. 任务管理优化

- 任务队列：异步处理机制
- 状态缓存：减少API调用
- 自动清理：过期任务清理
- 并发控制：限制同时处理数量

### 3. 用户体验优化

- 进度显示：实时状态更新
- 自动查询：定时状态检查
- 下载管理：批量下载支持
- 历史记录：任务历史查看

## 🔒 安全考虑

### 1. 文档安全

- 文件类型验证
- 恶意内容检测
- 临时存储加密
- 自动删除机制

### 2. API安全

- 访问令牌管理
- 请求频率限制
- 参数验证
- 错误信息过滤

### 3. 数据隐私

- 文档内容不持久化
- 传输过程加密
- 用户数据隔离
- 审计日志记录

## 📝 维护说明

### 1. 监控指标

- 任务创建成功率
- 翻译完成率
- 平均处理时间
- 错误率统计

### 2. 日常维护

- 清理过期任务
- 监控存储空间
- 检查API配额
- 更新错误处理

### 3. 故障排查

- 任务状态异常
- 文档下载失败
- API调用超时
- 格式转换错误

---

## 📞 技术支持

如有问题，请参考：

1. 百度AI开放平台官方文档
2. 项目内部技术文档
3. 开发团队技术支持

**实现完成时间**: 2025-07-29
**文档版本**: v1.0
**维护人员**: AI Assistant
