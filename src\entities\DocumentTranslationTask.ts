import { Entity, Column, Index, ManyToOne, JoinColumn } from 'typeorm';
import { BaseEntity } from './BaseEntity';
import { Client } from './Client';

/**
 * 文档翻译任务状态枚举
 */
export enum DocumentTranslationStatus {
  NOT_STARTED = 'NotStarted',
  RUNNING = 'Running',
  SUCCEEDED = 'Succeeded',
  FAILED = 'Failed',
  EXPIRED = 'Expired',
}

/**
 * 文档翻译任务实体
 */
@Entity('document_translation_tasks')
export class DocumentTranslationTask extends BaseEntity {
  @Column({
    length: 100,
    comment: '外部任务ID（百度翻译返回的任务ID）',
  })
  @Index()
  externalTaskId!: string;

  @Column({
    length: 36,
    comment: '客户端用户ID',
  })
  @Index()
  clientId!: string;

  @Column({
    length: 20,
    comment: '翻译供应商',
    default: 'baidu',
  })
  vendor!: string;

  @Column({
    length: 255,
    comment: '原始文件名',
  })
  originalFilename!: string;

  @Column({
    length: 10,
    comment: '文件格式',
  })
  fileFormat!: string;

  @Column({
    type: 'bigint',
    comment: '文件大小（字节）',
  })
  fileSize!: number;

  @Column({
    length: 10,
    comment: '源语言',
  })
  sourceLanguage!: string;

  @Column({
    length: 10,
    comment: '目标语言',
  })
  targetLanguage!: string;

  @Column({
    length: 50,
    comment: '垂直领域',
    nullable: true,
  })
  domain?: string;

  @Column({
    type: 'json',
    comment: '输出格式列表',
    nullable: true,
  })
  outputFormats?: string[];

  @Column({
    length: 100,
    comment: '文件名前缀',
    nullable: true,
  })
  filenamePrefix?: string;

  @Column({
    type: 'enum',
    enum: DocumentTranslationStatus,
    comment: '任务状态',
    default: DocumentTranslationStatus.NOT_STARTED,
  })
  @Index()
  status!: DocumentTranslationStatus;

  @Column({
    type: 'text',
    comment: '状态说明',
    nullable: true,
  })
  reason?: string;

  @Column({
    type: 'json',
    comment: '翻译结果文件信息',
    nullable: true,
  })
  resultFiles?: Array<{
    format: string;
    filename: string;
    size?: number;
    url?: string;
  }>;

  @Column({
    type: 'int',
    comment: '字符数量',
    nullable: true,
  })
  characterCount?: number;

  @Column({
    type: 'datetime',
    comment: '任务开始时间',
    nullable: true,
  })
  startedAt?: Date;

  @Column({
    type: 'datetime',
    comment: '任务完成时间',
    nullable: true,
  })
  completedAt?: Date;

  @Column({
    type: 'datetime',
    comment: '任务过期时间',
    nullable: true,
  })
  expiredAt?: Date;

  @Column({
    type: 'text',
    comment: '错误信息',
    nullable: true,
  })
  errorMessage?: string;

  @Column({
    type: 'json',
    comment: '原始API响应数据',
    nullable: true,
  })
  rawResponse?: any;

  // 关联客户端用户
  @ManyToOne(() => Client)
  @JoinColumn({ name: 'clientId' })
  client!: Client;
}
