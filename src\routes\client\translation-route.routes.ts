import Router from '@koa/router';
import { TranslationRouteController } from '../../controllers/client/translation-route.controller';
import { tenantMiddleware } from '../../middleware/tenant.middleware';
import { clientAuthMiddleware } from '../../middleware/auth.middleware';

const router = new Router({ prefix: '/api/client/translation-routes' });

router.use(tenantMiddleware).use(clientAuthMiddleware);

router
  .post('/create', (ctx) => new TranslationRouteController().createRoute(ctx))
  .put('/update/:id', (ctx) => new TranslationRouteController().updateRoute(ctx))
  .get('/list', (ctx) => new TranslationRouteController().getRoutes(ctx))
  .put('/toggle-status/:id', (ctx) => new TranslationRouteController().toggleStatus(ctx))
  .delete('/delete/:id', (ctx) => new TranslationRouteController().deleteRoute(ctx))
  .get('/vendors', (ctx) => new TranslationRouteController().getSupportedVendors(ctx));

export default router;
