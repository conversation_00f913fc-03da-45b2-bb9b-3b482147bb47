import { app, Menu, Tray } from "electron";
import path from 'node:path'
import { PUBLIC } from '../config/config';
import { coreWindowsManager } from '../windows/index'
let tray;
export function createTray() {
  if (!tray) {
    const trayIconPath = path.join(PUBLIC, 'favicon.ico');
    tray = new Tray(trayIconPath);
    const contextMenu = Menu.buildFromTemplate([
      {
        label: "主窗口",
        click: () => {
          const mainWin = coreWindowsManager.getWindow('main');
          if (mainWin) {
            mainWin.show();
            mainWin.focus();
          } else {
            coreWindowsManager.createMainWindow();
          }
        },
      },
      {
        label: "退出",
        click: () => {
          app.quit();
          process.exit(0);
        },
      },
    ]);
    tray.setContextMenu(contextMenu);
  }
  return tray;
}
