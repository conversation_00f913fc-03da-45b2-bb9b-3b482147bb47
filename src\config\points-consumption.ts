import { VendorType, TranslationType } from '../entities/TranslationRoute';

/**
 * 积分消耗配置
 * 按不同翻译供应商和翻译类型设置消耗积分数量
 */
export interface PointsConsumptionConfig {
  [vendor: string]: {
    [type: string]: number;
  };
}

/**
 * 默认积分消耗配置
 */
export const POINTS_CONSUMPTION: PointsConsumptionConfig = {
  [VendorType.BAIDU]: {
    [TranslationType.TEXT]: 1, // 百度文本翻译每次消耗1积分
    [TranslationType.IMAGE]: 5, // 百度图片翻译每次消耗5积分
    [TranslationType.DOCUMENT]: 10, // 百度文档翻译每次消耗10积分
    [TranslationType.AUDIO]: 8, // 百度音频翻译每次消耗8积分
    [TranslationType.VIDEO]: 15, // 百度视频翻译每次消耗15积分
  },
  [VendorType.YOUDAO]: {
    [TranslationType.TEXT]: 1, // 有道文本翻译每次消耗1积分
    [TranslationType.IMAGE]: 5, // 有道图片翻译每次消耗5积分
    [TranslationType.DOCUMENT]: 10, // 有道文档翻译每次消耗10积分
    [TranslationType.AUDIO]: 8, // 有道音频翻译每次消耗8积分
    [TranslationType.VIDEO]: 15, // 有道视频翻译每次消耗15积分
  },
  [VendorType.ALIBABA]: {
    [TranslationType.TEXT]: 1, // 阿里文本翻译每次消耗1积分
    [TranslationType.IMAGE]: 5, // 阿里图片翻译每次消耗5积分
    [TranslationType.DOCUMENT]: 10, // 阿里文档翻译每次消耗10积分
    [TranslationType.AUDIO]: 8, // 阿里音频翻译每次消耗8积分
    [TranslationType.VIDEO]: 15, // 阿里视频翻译每次消耗15积分
  },
  [VendorType.GOOGLE]: {
    [TranslationType.TEXT]: 2, // 谷歌文本翻译每次消耗2积分
    [TranslationType.IMAGE]: 8, // 谷歌图片翻译每次消耗8积分
    [TranslationType.DOCUMENT]: 15, // 谷歌文档翻译每次消耗15积分
    [TranslationType.AUDIO]: 12, // 谷歌音频翻译每次消耗12积分
    [TranslationType.VIDEO]: 20, // 谷歌视频翻译每次消耗20积分
  },
  [VendorType.DEEPL]: {
    [TranslationType.TEXT]: 2, // DeepL文本翻译每次消耗2积分
    [TranslationType.IMAGE]: 8, // DeepL图片翻译每次消耗8积分
    [TranslationType.DOCUMENT]: 15, // DeepL文档翻译每次消耗15积分
    [TranslationType.AUDIO]: 12, // DeepL音频翻译每次消耗12积分
    [TranslationType.VIDEO]: 20, // DeepL视频翻译每次消耗20积分
  },
};

/**
 * 获取指定供应商和翻译类型的积分消耗
 * @param vendor 翻译供应商
 * @param type 翻译类型
 * @returns 消耗的积分数量，如果未配置则返回默认值1
 */
export function getPointsConsumption(vendor: VendorType, type: TranslationType): number {
  const vendorConfig = POINTS_CONSUMPTION[vendor];
  if (!vendorConfig) {
    console.warn(`未找到供应商 ${vendor} 的积分消耗配置，使用默认值1`);
    return 1;
  }

  const consumption = vendorConfig[type];
  if (consumption === undefined) {
    console.warn(`未找到供应商 ${vendor} 翻译类型 ${type} 的积分消耗配置，使用默认值1`);
    return 1;
  }

  return consumption;
}

/**
 * 获取所有积分消耗配置
 * @returns 完整的积分消耗配置
 */
export function getAllPointsConsumption(): PointsConsumptionConfig {
  return POINTS_CONSUMPTION;
}

/**
 * 检查是否支持指定的供应商和翻译类型
 * @param vendor 翻译供应商
 * @param type 翻译类型
 * @returns 是否支持
 */
export function isSupportedVendorType(vendor: VendorType, type: TranslationType): boolean {
  const vendorConfig = POINTS_CONSUMPTION[vendor];
  return vendorConfig && vendorConfig[type] !== undefined;
}
