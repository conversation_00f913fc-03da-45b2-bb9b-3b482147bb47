import { join } from 'node:path'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
const __dirname = path.dirname(fileURLToPath(import.meta.url))

export const DIST_ELECTRON = join(__dirname, '../..')
export const DIST = join(DIST_ELECTRON, 'dist')
export const PUBLIC = process.env.VITE_DEV_SERVER_URL
    ? join(DIST_ELECTRON, 'public')
    : DIST

export const INDEX_HTML = join(DIST, 'index.html')

export const DEV_SERVER_URL = process.env.VITE_DEV_SERVER_URL

export const PRELOAD = join(__dirname, '../preload/index.js')
