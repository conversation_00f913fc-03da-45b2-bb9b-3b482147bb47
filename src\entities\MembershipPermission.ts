import { <PERSON><PERSON>ty, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseEntity } from './BaseEntity';
import { MembershipProduct } from './MembershipProduct';
import { Permission } from './Permission';

/**
 * 会员权限关联实体
 */
@Entity('membership_permissions')
export class MembershipPermission extends BaseEntity {
  @ManyToOne(() => MembershipProduct, (membershipProduct) => membershipProduct.permissions)
  @JoinColumn({ name: 'membership_product_id' })
  membershipProduct!: MembershipProduct;

  @ManyToOne(() => Permission, (permission) => permission.membershipPermissions)
  @JoinColumn({ name: 'permission_id' })
  permission!: Permission;
}
