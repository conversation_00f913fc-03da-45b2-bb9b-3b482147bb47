import { Entity, Column, ManyToOne, Join<PERSON><PERSON>umn } from 'typeorm';
import { BaseEntity } from './BaseEntity';
import { Client } from './Client';
import { MembershipProduct } from './MembershipProduct';

/**
 * 会员订单实体
 */
@Entity('membership_orders')
export class MembershipOrder extends BaseEntity {
  @Column({
    length: 50,
    comment: '订单号',
    unique: true,
  })
  orderNo!: string;

  @ManyToOne(() => Client)
  @JoinColumn({ name: 'client_id' })
  client!: Client;

  @ManyToOne(() => MembershipProduct)
  @JoinColumn({ name: 'membership_product_id' })
  membershipProduct!: MembershipProduct;

  @Column({
    comment: '订单金额(分)',
    type: 'int',
  })
  amount!: number;

  @Column({
    comment: '支付方式: wechat-微信, alipay-支付宝',
    length: 20,
    nullable: true,
  })
  paymentMethod?: string;

  @Column({
    comment: '支付状态: pending-待支付, paid-已支付, cancelled-已取消',
    length: 20,
    default: 'pending',
  })
  status!: string;

  @Column({
    comment: '支付时间',
    type: 'datetime',
    nullable: true,
  })
  paidAt?: Date;

  @Column({
    comment: '交易流水号',
    nullable: true,
  })
  transactionId?: string;

  @Column({
    comment: '购买的会员时长(天)',
    type: 'int',
  })
  duration!: number;
}
