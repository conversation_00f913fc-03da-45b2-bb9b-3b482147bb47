import { Context } from 'koa';
import { PermissionService } from '../../services/permission.service';

interface PermissionBody {
  name: string;
  key: string;
  description?: string;
  group?: string;
  isActive?: boolean;
}

/**
 * 管理员权限控制器
 */
export class AdminPermissionController {
  private permissionService: PermissionService;

  constructor() {
    this.permissionService = new PermissionService();
  }

  /**
   * 获取权限列表
   */
  public getList = async (ctx: Context): Promise<void> => {
    try {
      const page = parseInt(ctx.query.page as string) || 1;
      const pageSize = parseInt(ctx.query.pageSize as string) || 10;

      const result = await this.permissionService.findPage(page, pageSize, {
        order: { group: 'ASC', createdAt: 'DESC' },
      });

      ctx.body = {
        code: 0,
        data: result,
        message: '获取权限列表成功',
      };
    } catch (error) {
      console.error('获取权限列表失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取权限列表失败',
      };
    }
  };

  /**
   * 获取权限列表（按分组返回）
   */
  public getGroupList = async (ctx: Context): Promise<void> => {
    try {
      const result = await this.permissionService.getPermissionsByGroup();

      ctx.body = {
        code: 0,
        data: result,
        message: '获取权限列表成功',
      };
    } catch (error) {
      console.error('获取权限列表失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取权限列表失败',
      };
    }
  };

  /**
   * 获取权限详情
   */
  public getDetail = async (ctx: Context): Promise<void> => {
    try {
      const { id } = ctx.params;

      const result = await this.permissionService.findOne(id);

      if (!result) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '权限不存在',
        };
        return;
      }

      ctx.body = {
        code: 0,
        data: result,
        message: '获取权限详情成功',
      };
    } catch (error) {
      console.error('获取权限详情失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取权限详情失败',
      };
    }
  };

  /**
   * 创建权限
   */
  public create = async (ctx: Context): Promise<void> => {
    try {
      const { name, key, description, group, isActive } = ctx.request.body as PermissionBody;

      // 验证必填字段
      if (!name || !key) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '请填写权限名称和权限标识',
        };
        return;
      }

      // 检查权限标识是否已存在
      const exists = await this.permissionService.isKeyExists(key);
      if (exists) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '权限标识已存在',
        };
        return;
      }

      const result = await this.permissionService.create({
        name,
        key,
        description,
        group,
        isActive,
      });

      ctx.body = {
        code: 0,
        data: result,
        message: '创建权限成功',
      };
    } catch (error) {
      console.error('创建权限失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '创建权限失败',
      };
    }
  };

  /**
   * 更新权限
   */
  public update = async (ctx: Context): Promise<void> => {
    try {
      const { id } = ctx.params;
      const { name, key, description, group, isActive } = ctx.request.body as PermissionBody;

      // 检查权限标识是否已存在
      if (key) {
        const exists = await this.permissionService.isKeyExists(key, id);
        if (exists) {
          ctx.status = 400;
          ctx.body = {
            code: 400,
            message: '权限标识已存在',
          };
          return;
        }
      }

      const result = await this.permissionService.update(id, {
        name,
        key,
        description,
        group,
        isActive,
      });

      if (!result) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '权限不存在',
        };
        return;
      }

      ctx.body = {
        code: 0,
        data: result,
        message: '更新权限成功',
      };
    } catch (error) {
      console.error('更新权限失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '更新权限失败',
      };
    }
  };

  /**
   * 删除权限
   */
  public delete = async (ctx: Context): Promise<void> => {
    try {
      const { id } = ctx.params;

      const result = await this.permissionService.remove(id);

      if (!result) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '权限不存在',
        };
        return;
      }

      ctx.body = {
        code: 0,
        message: '删除权限成功',
      };
    } catch (error) {
      console.error('删除权限失败', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '删除权限失败',
      };
    }
  };
}
