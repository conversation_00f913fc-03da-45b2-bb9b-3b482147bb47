<template>
    <div class="w-fit h-full pl-1 overflow-y-auto scrollbar-hide">
        <div class="bg-white rounded-lg w-[300px] h-full p-3 flex flex-col relative">
            <div class="font-bold flex-shrink-0">代理设置</div>
            <div class="pt-4 flex-grow h-0 overflow-auto scrollbar-hide pb-16">
                <div>
                    <el-form ref="formRef" :model="form" :rules="rules" label-width="auto" label-position="top">
                        <el-form-item label="启用代理服务器" label-position="left">
                            <div class="w-full flex justify-end">
                                <el-switch v-model="form.enabled" />
                            </div>
                        </el-form-item>
                        <template v-if="form.enabled">
                            <el-form-item label="协议" label-position="top" prop="protocol">
                                <el-select v-model="form.protocol" placeholder="请选择协议">
                                    <el-option v-for="item in [
                                        { label: 'HTTP', value: 'http' },
                                        { label: 'HTTPS', value: 'https' },
                                        { label: 'SOCKS5', value: 'socks5' }
                                    ]" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="主机" label-position="top" prop="host">
                                <el-input v-model="form.host" placeholder="请输入主机地址" />
                            </el-form-item>
                            <el-form-item label="端口" label-position="top" prop="port">
                                <el-input v-model="form.port" placeholder="请输入端口" />
                            </el-form-item>
                            <el-form-item label="是否需要登录" label-position="left">
                                <div class="w-full flex justify-end">
                                    <el-switch v-model="form.auth" />
                                </div>
                            </el-form-item>
                            <template v-if="form.auth">
                                <el-form-item label="用户名" label-position="top" prop="username">
                                    <el-input v-model="form.username" placeholder="请输入用户名" />
                                </el-form-item>
                                <el-form-item label="密码" label-position="top" prop="password">
                                    <el-input v-model="form.password" type="password" placeholder="请输入密码"
                                        show-password />
                                </el-form-item>
                            </template>
                        </template>
                    </el-form>
                </div>
            </div>
            <div class="absolute bottom-0 left-0 right-0 p-3 bg-white border-t">
                <div class="flex justify-end gap-2 w-full">
                    <el-button class="flex-grow" @click="resetForm">重置</el-button>
                    <el-button class="flex-grow" type="primary" @click="saveConfig">保存</el-button>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { $message, $confirm } from '@/utils/message'
import { useChatStore } from '../../../../../stores/chat'
import type { FormInstance, FormRules } from 'element-plus'
import { IChatAccountsBo } from '@/api/chat';

interface ProxyConfig {
    enabled: boolean,
    protocol: string,
    host: string,
    port: string,
    auth: boolean,
    username: string,
    password: string
}

const chatStore = useChatStore();
const props = defineProps<{
    chatId: string,
    chatInfo?: IChatAccountsBo
}>()

const formRef = ref<FormInstance>()

// 表单校验规则
const rules = ref<FormRules>({
    protocol: [
        { required: true, message: '请选择协议', trigger: 'change' }
    ],
    host: [
        { required: true, message: '请输入主机地址', trigger: 'blur' },
        { pattern: /^[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})*$/, message: '请输入正确的主机地址', trigger: 'blur' }
    ],
    port: [
        { required: true, message: '请输入端口', trigger: 'blur' },
        { pattern: /^([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/, message: '端口范围为1-65535', trigger: 'blur' }
    ],
    username: [
        { required: true, message: '请输入用户名', trigger: 'blur' }
    ],
    password: [
        { required: true, message: '请输入密码', trigger: 'blur' }
    ]
})

// 从本地缓存读取配置
const loadConfig = async (): Promise<ProxyConfig> => {
    try {
        // 从 Electron 获取配置
        const config = await window.ipcRenderer.invoke('get-proxy-config', props.chatId)
        if (config) {
            return config as ProxyConfig
        }
    } catch (error) {
        console.error('从 Electron 获取配置失败:', error)
    }
    return {
        enabled: false,
        protocol: '',
        host: '',
        port: '',
        auth: false,
        username: '',
        password: ''
    }
}
const defaultConfig: ProxyConfig = {
    enabled: false,
    protocol: '',
    host: '',
    port: '',
    auth: false,
    username: '',
    password: ''
}

const form = ref(defaultConfig)

// 保存配置
const saveConfig = async () => {
    if (!form.value.enabled) {
        try {
            // 转换为纯对象
            const config = JSON.parse(JSON.stringify(form.value))
            await window.ipcRenderer.invoke('set-proxy-config', props.chatId, config)
            console.log(props.chatId)
            if (!props.chatInfo?.isLoaded) {
                $message.success("保存成功!")
            } else {
                $confirm('保存成功，是否需要重启？', '提示', {
                    type: 'warning',
                    confirmButtonText: '立即重启',
                    cancelButtonText: '稍后重启'
                }).then(async () => {
                    chatStore.closeChat(props.chatId)
                })
            }
        } catch (error) {
            if (error !== 'cancel') {
                console.error('保存配置到 Electron 失败:', error)
                $message.error('保存配置失败')
            }
        }
        return
    }

    if (!formRef.value) return

    await formRef.value.validate(async (valid, fields) => {
        if (valid) {
            // 如果不需要认证，清空用户名和密码字段
            if (!form.value.auth) {
                form.value.username = ''
                form.value.password = ''
            }
            try {
                // 转换为纯对象
                const config = JSON.parse(JSON.stringify(form.value))
                await window.ipcRenderer.invoke('set-proxy-config', props.chatId, config);
                if (!props.chatInfo?.isLoaded) {
                    $message.success("保存成功!")
                } else {
                    $confirm('保存成功，是否需要重启？', '提示', {
                        type: 'warning',
                        confirmButtonText: '立即重启',
                        cancelButtonText: '稍后重启'
                    }).then(async () => {
                        chatStore.closeChat(props.chatId)
                    })
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('保存配置到 Electron 失败:', error)
                    $message.error('保存配置失败')
                }
            }
        } else {
            console.log('校验失败', fields)
            throw new Error('表单校验失败')
        }
    })
}

// 重置表单
const resetForm = async () => {
    form.value = await loadConfig()
    formRef.value?.clearValidate()
    $message.success('已重置')
}

// 监听聊天ID变化，重新加载配置
watch(() => props.chatId, async () => {
    form.value = await loadConfig()
    formRef.value?.clearValidate()
})

// 监听启用状态变化，清除校验信息
watch(() => form.value.enabled, (newVal) => {
    if (!newVal) {
        formRef.value?.clearValidate()
    }
})

// 监听认证状态变化，清除用户名密码的校验信息
watch(() => form.value.auth, (newVal) => {
    if (!newVal) {
        formRef.value?.clearValidate(['username', 'password'])
    }
})

onMounted(async () => {
    form.value = await loadConfig()
})
</script>

<style>
/* 自定义滚动条隐藏 */
.scrollbar-hide {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari and Opera */
}
</style>