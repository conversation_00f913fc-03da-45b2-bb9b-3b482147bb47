import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitialSchema1710000000000 implements MigrationInterface {
  name = 'InitialSchema1710000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建 accounts 表
    await queryRunner.query(`
            CREATE TABLE accounts (
                id VARCHAR(36) PRIMARY KEY,
                createdAt DATETIME NOT NULL,
                updatedAt DATETIME NOT NULL,
                tenantId VARCHAR(36) NOT NULL DEFAULT '0',
                name VARCHAR(100) NOT NULL,
                email VARCHAR(255) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                isActive BOOLEAN NOT NULL DEFAULT true,
                isSystemAdmin BOOLEAN NOT NULL DEFAULT false,
                phone VARCHAR(20),
                resetPasswordToken VARCHAR(255),
                resetPasswordExpires DATETIME,
                INDEX idx_accounts_tenant (tenantId),
                INDEX idx_accounts_name (name)
            )
        `);

    // 创建 clients 表
    await queryRunner.query(`
            CREATE TABLE clients (
                id VARCHAR(36) PRIMARY KEY,
                createdAt DATETIME NOT NULL,
                updatedAt DATETIME NOT NULL,
                tenantId VARCHAR(36) NOT NULL DEFAULT '0',
                name VARCHAR(128) NOT NULL,
                email VARCHAR(255) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                phone VARCHAR(20),
                isActive BOOLEAN NOT NULL DEFAULT true,
                resetPasswordToken VARCHAR(255),
                resetPasswordExpires DATETIME,
                INDEX idx_clients_tenant (tenantId)
            )
        `);

    // 创建 membership_products 表
    await queryRunner.query(`
            CREATE TABLE membership_products (
                id VARCHAR(36) PRIMARY KEY,
                createdAt DATETIME NOT NULL,
                updatedAt DATETIME NOT NULL,
                tenantId VARCHAR(36) NOT NULL DEFAULT '0',
                name VARCHAR(100) NOT NULL,
                description TEXT,
                price DECIMAL(10,2) NOT NULL,
                duration INT NOT NULL,
                isActive BOOLEAN NOT NULL DEFAULT true,
                INDEX idx_membership_products_tenant (tenantId)
            )
        `);

    // 创建 permissions 表
    await queryRunner.query(`
            CREATE TABLE permissions (
                id VARCHAR(36) PRIMARY KEY,
                createdAt DATETIME NOT NULL,
                updatedAt DATETIME NOT NULL,
                tenantId VARCHAR(36) NOT NULL DEFAULT '0',
                name VARCHAR(100) NOT NULL,
                code VARCHAR(100) NOT NULL,
                description TEXT,
                isActive BOOLEAN NOT NULL DEFAULT true,
                INDEX idx_permissions_tenant (tenantId),
                UNIQUE KEY uk_permissions_code (code)
            )
        `);

    // 创建 membership_permissions 表
    await queryRunner.query(`
            CREATE TABLE membership_permissions (
                id VARCHAR(36) PRIMARY KEY,
                createdAt DATETIME NOT NULL,
                updatedAt DATETIME NOT NULL,
                tenantId VARCHAR(36) NOT NULL DEFAULT '0',
                membershipProductId VARCHAR(36) NOT NULL,
                permissionId VARCHAR(36) NOT NULL,
                INDEX idx_membership_permissions_tenant (tenantId),
                INDEX idx_membership_permissions_product (membershipProductId),
                INDEX idx_membership_permissions_permission (permissionId),
                FOREIGN KEY (membershipProductId) REFERENCES membership_products(id) ON DELETE CASCADE,
                FOREIGN KEY (permissionId) REFERENCES permissions(id) ON DELETE CASCADE
            )
        `);

    // 创建 client_memberships 表
    await queryRunner.query(`
            CREATE TABLE client_memberships (
                id VARCHAR(36) PRIMARY KEY,
                createdAt DATETIME NOT NULL,
                updatedAt DATETIME NOT NULL,
                tenantId VARCHAR(36) NOT NULL DEFAULT '0',
                clientId VARCHAR(36) NOT NULL,
                membershipProductId VARCHAR(36) NOT NULL,
                startDate DATETIME NOT NULL,
                endDate DATETIME NOT NULL,
                isActive BOOLEAN NOT NULL DEFAULT true,
                INDEX idx_client_memberships_tenant (tenantId),
                INDEX idx_client_memberships_client (clientId),
                INDEX idx_client_memberships_product (membershipProductId),
                FOREIGN KEY (clientId) REFERENCES clients(id) ON DELETE CASCADE,
                FOREIGN KEY (membershipProductId) REFERENCES membership_products(id) ON DELETE CASCADE
            )
        `);

    // 创建 membership_orders 表
    await queryRunner.query(`
            CREATE TABLE membership_orders (
                id VARCHAR(36) PRIMARY KEY,
                createdAt DATETIME NOT NULL,
                updatedAt DATETIME NOT NULL,
                tenantId VARCHAR(36) NOT NULL DEFAULT '0',
                clientId VARCHAR(36) NOT NULL,
                membershipProductId VARCHAR(36) NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                status VARCHAR(20) NOT NULL,
                paymentMethod VARCHAR(50),
                paymentTime DATETIME,
                INDEX idx_membership_orders_tenant (tenantId),
                INDEX idx_membership_orders_client (clientId),
                INDEX idx_membership_orders_product (membershipProductId),
                FOREIGN KEY (clientId) REFERENCES clients(id) ON DELETE CASCADE,
                FOREIGN KEY (membershipProductId) REFERENCES membership_products(id) ON DELETE CASCADE
            )
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 按照依赖关系的反序删除表
    await queryRunner.query(`DROP TABLE IF EXISTS membership_orders`);
    await queryRunner.query(`DROP TABLE IF EXISTS client_memberships`);
    await queryRunner.query(`DROP TABLE IF EXISTS membership_permissions`);
    await queryRunner.query(`DROP TABLE IF EXISTS permissions`);
    await queryRunner.query(`DROP TABLE IF EXISTS membership_products`);
    await queryRunner.query(`DROP TABLE IF EXISTS clients`);
    await queryRunner.query(`DROP TABLE IF EXISTS accounts`);
  }
}
