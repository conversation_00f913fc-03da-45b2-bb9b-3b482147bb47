import { Rectangle } from "electron";
import { IEmitType, ICoreWindowId } from "../enums";
import coreWindowsManager from '../windows/coreWindow'
import { IAccountInfo } from "../interface";

export interface IChatWindowEmitData {
    avatar?: string;
    accountId?: string;
    nickname?: string;
    chatId: string;
    isLogined: boolean;
    isLoading: boolean;
    isShow: boolean;
    isCreated: boolean;
    unreadCount: number;
}

export function chatWindowEmit(data: IChatWindowEmitData) {
    coreWindowsManager.sendMessageToRenderer(ICoreWindowId.Main, IEmitType.ChatWindowEmit, data);
}

// 会话窗口创建
export function chatWindowCreatedEmit(chatId: string) {
    coreWindowsManager.sendMessageToRenderer(ICoreWindowId.Main, IEmitType.OnChatWindowCreated, chatId);
}

// 会话窗口显示
export function chatWindowShowEmit(chatId: string) {
    coreWindowsManager.sendMessageToRenderer(ICoreWindowId.Main, IEmitType.OnChatWindowShow, chatId);
}

// 会话窗口隐藏
export function chatWindowHideEmit(chatId: string) {
    coreWindowsManager.sendMessageToRenderer(ICoreWindowId.Main, IEmitType.OnChatWindowHide, chatId);
}

// 会话窗口关闭
export function chatWindowClosedEmit(chatId: string) {
    coreWindowsManager.sendMessageToRenderer(ICoreWindowId.Main, IEmitType.OnChatWindowClosed, chatId);
}

// 会话窗口尺寸变化
export function chatWindowResizedEmit(bounds: Rectangle) {
    coreWindowsManager.sendMessageToRenderer(ICoreWindowId.Main, IEmitType.OnChatWindowResized, bounds);
}

// 窗口开始加载
export function chatWindowStartLoadEmit(chatId: string) {
    coreWindowsManager.sendMessageToRenderer(ICoreWindowId.Main, IEmitType.OnChatWindowStartLoading, chatId);
}

// 窗口加载完成
export function chatWindowReadyedEmit(chatId: string) {
    coreWindowsManager.sendMessageToRenderer(ICoreWindowId.Main, IEmitType.OnChatWindowReadyed, chatId);
}

// 窗口加载失败
export function chatWindowLoadFailEmit(chatId: string) {
    coreWindowsManager.sendMessageToRenderer(ICoreWindowId.Main, IEmitType.OnChatWindowLoadFail, chatId);
}

// 平台账号信息变化
export function platformAccountEmit(chatId: string, accountInfo: IAccountInfo) {
    coreWindowsManager.sendMessageToRenderer(ICoreWindowId.Main, IEmitType.OnPlatformAccountChange, chatId, accountInfo);
}