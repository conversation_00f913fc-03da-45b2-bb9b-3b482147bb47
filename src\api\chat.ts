import { PlatformKeys } from '../constants/enum';
import { IChatWindowInfo } from '../types';
import request from '../utils/request';

export interface IChatAccountsBo extends IChatWindowInfo {
    id: string,
    createdAt: string,
    updatedAt: string,
    tenantId: string,
    platform: PlatformKeys,
}

export function createChatAccountAPI(platform: PlatformKeys) {
    return request<IChatAccountsBo[]>({
        url: '/client/chat/accounts',
        method: 'POST',
        data: {
            platform
        }
    })
}

export function listChatAccountAPI() {
    return request({
        url: '/client/chat/accounts',
        method: 'get',
    })
}

export function deleteChatAccountAPI(id: string) {
    return request({
        url: `/client/chat/accounts/${id}`,
        method: 'delete',
    })
}