# SCRM 服务端

## 项目说明
这是一个基于 TypeScript + Koa + TypeORM 开发的 SCRM（Social Customer Relationship Management）系统服务端。

## 技术栈
- TypeScript
- Koa
- TypeORM
- MySQL
- Redis
- JWT

## 环境要求
- Node.js >= 14
- MySQL >= 5.7
- Redis >= 6.0

## 安装和运行

1. 安装依赖
```bash
npm install
```

2. 配置环境变量
创建 `.env` 文件并配置以下环境变量：
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=root
DB_DATABASE=scrm

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=7d

# 邮件配置
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password
EMAIL_FROM=<EMAIL>
```

3. 运行开发环境
```bash
npm run dev
```

4. 构建生产环境
```bash
npm run build
npm start
```

## 数据库迁移

### 迁移命令说明

1. 生成迁移文件
```bash
npm run migration:generate <迁移名称>
```
例如：`npm run migration:generate AddUserTable`

2. 运行迁移
```bash
npm run migration:run
```

3. 回滚迁移
```bash
npm run migration:revert
```

### 迁移文件说明

迁移文件位于 `src/migrations` 目录下，每个迁移文件包含两个主要方法：

- `up()`: 定义如何应用迁移（创建表、添加字段等）
- `down()`: 定义如何回滚迁移（删除表、删除字段等）

### 初始迁移

项目包含一个初始迁移文件 `*************-InitialSchema.ts`，其中包含了所有基础表结构：

- accounts: 账号表
- clients: 客户端用户表
- membership_products: 会员产品表
- permissions: 权限表
- membership_permissions: 会员权限关联表
- client_memberships: 客户会员关联表
- membership_orders: 会员订单表

### 使用建议

1. 首次部署时，运行初始迁移：
```bash
npm run migration:run
```

2. 当需要修改数据库结构时：
   - 修改实体类（`src/entities` 目录下的文件）
   - 生成迁移文件：`npm run migration:generate <迁移名称>`
   - 检查生成的迁移文件内容
   - 运行迁移：`npm run migration:run`

3. 如果迁移出现问题需要回滚：
```bash
npm run migration:revert
```

## 开发规范

1. 代码格式化
```bash
npm run format
```

2. 代码检查
```bash
npm run lint
```

3. 自动修复代码问题
```bash
npm run lint:fix
```

## 目录结构

```
src/
├── config/         # 配置文件
├── controllers/    # 控制器
├── entities/       # 数据库实体
├── interfaces/     # 接口定义
├── middleware/     # 中间件
├── migrations/     # 数据库迁移文件
├── routes/         # 路由
├── services/       # 服务层
├── utils/          # 工具函数
└── index.ts        # 入口文件
```

## 注意事项

1. 数据库迁移
   - 在生产环境中，请谨慎使用迁移功能
   - 执行迁移前请备份数据库
   - 迁移文件生成后，请仔细检查 SQL 语句的正确性

2. 环境变量
   - 请确保所有必要的环境变量都已正确配置
   - 生产环境的敏感信息（如数据库密码、JWT密钥等）应该妥善保管

3. 安全建议
   - 定期更新依赖包版本
   - 使用强密码
   - 启用 HTTPS
   - 实施适当的访问控制

## API 文档

### 用户端接口

#### 注册

- URL: `/api/user/auth/register`
- 方法: POST
- 请求体:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123",
    "name": "用户名"
  }
  ```
- 响应:
  ```json
  {
    "code": 200,
    "message": "注册成功",
    "data": {
      "user": { /* 用户信息 */ },
      "token": "jwt_token"
    }
  }
  ```

#### 登录

- URL: `/api/user/auth/login`
- 方法: POST
- 请求体:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- 响应:
  ```json
  {
    "code": 200,
    "message": "登录成功",
    "data": {
      "user": { /* 用户信息 */ },
      "token": "jwt_token"
    }
  }
  ```

#### 忘记密码

- URL: `/api/user/auth/forgot-password`
- 方法: POST
- 请求体:
  ```json
  {
    "email": "<EMAIL>"
  }
  ```
- 响应:
  ```json
  {
    "code": 200,
    "message": "重置密码邮件已发送"
  }
  ```

#### 重置密码

- URL: `/api/user/auth/reset-password`
- 方法: POST
- 请求体:
  ```json
  {
    "token": "reset_token",
    "password": "new_password"
  }
  ```
- 响应:
  ```json
  {
    "code": 200,
    "message": "密码重置成功"
  }
  ```

### 管理端接口

所有管理端接口需要在请求头中携带 `Authorization: Bearer <token>` 进行身份验证。

#### 用户列表

- URL: `/api/admin/users`
- 方法: GET
- 响应:
  ```json
  {
    "code": 200,
    "message": "成功",
    "data": [
      { /* 用户信息 */ }
    ]
  }
  ```

#### 分页获取用户

- URL: `/api/admin/users/page?page=1&pageSize=10`
- 方法: GET
- 响应:
  ```json
  {
    "code": 200,
    "message": "成功",
    "data": {
      "items": [
        { /* 用户信息 */ }
      ],
      "total": 100,
      "page": 1,
      "pageSize": 10,
      "totalPages": 10
    }
  }
  ```

## 技术栈

- Node.js
- TypeScript
- Koa
- TypeORM
- MySQL
- JWT
- Nodemailer

## 目录结构

```
├── src/
│   ├── config/         # 配置文件
│   ├── controllers/    # 控制器
│   ├── entities/       # 实体模型
│   ├── interfaces/     # 接口定义
│   ├── middleware/     # 中间件
│   ├── repositories/   # 仓库
│   ├── routes/         # 路由
│   ├── services/       # 服务
│   ├── utils/          # 工具函数
│   └── index.ts        # 入口文件
├── .env                # 环境变量
├── .eslintrc.js        # ESLint 配置
├── .prettierrc         # Prettier 配置
├── commitlint.config.js# CommitLint 配置
├── tsconfig.json       # TypeScript 配置
└── package.json        # 项目依赖
``` 