/* eslint-disable */
import 'reflect-metadata';
import { config } from 'dotenv';
import { AppDataSource } from '../src/config/database';
import { PointsProductService } from '../src/services/points-product.service';

// 加载环境变量
config();

/**
 * 积分商品数据
 */
const pointsProductsData = [
  {
    name: '100积分',
    description: '基础积分包，适合轻度使用',
    points: 100,
    price: 1000, // 10元，单位：分
    discountRate: 1.0,
    bonusPoints: 0,
    isActive: true,
    sort: 1,
  },
  {
    name: '500积分',
    description: '热门积分包，性价比高',
    points: 500,
    price: 4500, // 45元，单位：分
    discountRate: 0.9, // 9折
    bonusPoints: 50, // 赠送50积分
    isActive: true,
    sort: 2,
  },
  {
    name: '1000积分',
    description: '大容量积分包，重度用户首选',
    points: 1000,
    price: 8000, // 80元，单位：分
    discountRate: 0.8, // 8折
    bonusPoints: 200, // 赠送200积分
    isActive: true,
    sort: 3,
  },
  {
    name: '2000积分',
    description: '超值积分包，企业用户推荐',
    points: 2000,
    price: 15000, // 150元，单位：分
    discountRate: 0.75, // 7.5折
    bonusPoints: 500, // 赠送500积分
    isActive: true,
    sort: 4,
  },
  {
    name: '5000积分',
    description: '豪华积分包，无限畅享翻译服务',
    points: 5000,
    price: 35000, // 350元，单位：分
    discountRate: 0.7, // 7折
    bonusPoints: 1500, // 赠送1500积分
    isActive: true,
    sort: 5,
  },
  {
    name: '10000积分',
    description: '至尊积分包，专业用户专享',
    points: 10000,
    price: 60000, // 600元，单位：分
    discountRate: 0.6, // 6折
    bonusPoints: 4000, // 赠送4000积分
    isActive: true,
    sort: 6,
  },
];

/**
 * 导入积分商品数据
 */
async function importPointsProducts() {
  try {
    console.log('开始连接数据库...');
    await AppDataSource.initialize();
    console.log('数据库连接成功');

    const pointsProductService = new PointsProductService();

    console.log('开始导入积分商品数据...');

    // 检查是否已有数据
    const existingProducts = await pointsProductService.findAll();
    if (existingProducts.length > 0) {
      console.log(`发现已有 ${existingProducts.length} 个积分商品，是否继续导入？`);
      console.log('如需重新导入，请先清空 points_products 表');
      return;
    }

    // 批量创建积分商品
    const createdProducts = await pointsProductService.batchCreate(pointsProductsData);

    console.log(`成功导入 ${createdProducts.length} 个积分商品：`);
    createdProducts.forEach((product, index) => {
      console.log(`${index + 1}. ${product.name} - ${product.points}积分 - ¥${(product.actualPrice / 100).toFixed(2)} (原价¥${(product.price / 100).toFixed(2)})`);
      if (product.bonusPoints > 0) {
        console.log(`   赠送积分: ${product.bonusPoints}`);
      }
      console.log(`   总积分: ${product.totalPoints}`);
      console.log('');
    });

    console.log('积分商品数据导入完成！');
  } catch (error) {
    console.error('导入积分商品数据失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('数据库连接已关闭');
    }
  }
}

/**
 * 清空积分商品数据
 */
async function clearPointsProducts() {
  try {
    console.log('开始连接数据库...');
    await AppDataSource.initialize();
    console.log('数据库连接成功');

    const pointsProductService = new PointsProductService();
    const existingProducts = await pointsProductService.findAll();

    if (existingProducts.length === 0) {
      console.log('没有找到积分商品数据');
      return;
    }

    console.log(`找到 ${existingProducts.length} 个积分商品，开始清空...`);

    for (const product of existingProducts) {
      await pointsProductService.delete(product.id);
      console.log(`已删除: ${product.name}`);
    }

    console.log('积分商品数据清空完成！');
  } catch (error) {
    console.error('清空积分商品数据失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('数据库连接已关闭');
    }
  }
}

// 根据命令行参数执行不同操作
const action = process.argv[2];

if (action === 'clear') {
  clearPointsProducts();
} else {
  importPointsProducts();
}

// 使用说明
if (!action) {
  console.log('\n使用说明:');
  console.log('导入积分商品数据: npm run import-points-products');
  console.log('清空积分商品数据: npm run import-points-products clear');
}
