// Pinia Store
import { defineStore } from 'pinia'
import { createChatAccountAPI, IChatAccountsBo, listChatAccountAPI, deleteChatAccountAPI } from '../api/chat'
import { IEventType, PlatformKeys } from '../constants/enum';
import { Rectangle } from 'electron';
import { getChatWindowInfo, getChatWindowInfoList } from '../utils/ipcRenderer';
import { IAccountInfo, IChatWindowStatus } from '../types';

interface IState {
    chatList: IChatAccountsBo[],
    selectedChatId: string
};

const defatltChatWindowInfo: IChatWindowStatus = {
    isLoading: false,
    isShow: false,
    isLoaded: false,
    isLoadFail: false,
    isCreated: false,
}

export const useChatStore = defineStore('chat', {
    // 转换为函数
    state: (): IState => ({
        chatList: [],
        selectedChatId: ''
    }),
    actions: {
        async loadChatList() {
            const res = await listChatAccountAPI();
            const chatWindowInfoList = await getChatWindowInfoList();
            this.chatList = res.data.map((item: { id: any; }) => {
                const chatWindowInfo = chatWindowInfoList.find((i: { winId: any; }) => i.winId === item.id) || defatltChatWindowInfo;
                return {
                    ...item,
                    ...chatWindowInfo
                }
            });
        },
        async createChatAccount(platform: PlatformKeys) {
            const data: any = await createChatAccountAPI(platform)
            await this.loadChatList();
            this.showChat(data.data.id)
        },
        async createChatWindow(id: string, platform: string, bounds: Rectangle) {
            await window.ipcRenderer?.invoke(IEventType.CreateChatWindow, id, platform, bounds)
        },
        async showChat(id: string) {
            await window.ipcRenderer?.invoke(IEventType.ShowChatWindow, id);
            const index = this.chatList.findIndex(i => i.id === id);
            const chatWindowInfo = await getChatWindowInfo(id);
            this.chatList[index] = { ...this.chatList[index], ...chatWindowInfo };
            this.setSelectedChatId(id)
        },
        async closeChat(id: string) {
            await window.ipcRenderer?.invoke(IEventType.CloseChatWindow, id);
            const index = this.chatList.findIndex(i => i.id === id);
            const chatWindowInfo = await getChatWindowInfo(id);
            this.chatList[index] = { ...this.chatList[index], ...chatWindowInfo };
        },
        async deleteChat(id: string) {
            await this.closeChat(id)
            await deleteChatAccountAPI(id);
            await this.loadChatList();
            if (id === this.selectedChatId) {
                this.setSelectedChatId(this.chatList[0]?.id || '')
            }
        },
        async reloadChat(id: string) {
            await window.ipcRenderer?.invoke(IEventType.ReloadChatWindow, id);
        },
        setSelectedChatId(id: string) {
            this.selectedChatId = id;
        },
        setChatWindowState(id: string, key: keyof IChatWindowStatus, value: boolean) {
            for (const win of this.chatList) {
                if (win.id === id) {
                    win[key] = value
                    break
                }
            }
        },
        setChatWindowAccountInfo(id: string, accountInfo: IAccountInfo) {
            const index = this.chatList.findIndex(i => i.id === id);
            if (index >= 0) {
                this.chatList[index] = {
                    ...this.chatList[index],
                    ...accountInfo
                }
            }
        }
    },
})
