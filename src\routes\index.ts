import Koa from 'koa';

// 管理端路由
import adminAuthRoutes from './admin/auth.routes';
import adminMembershipRoutes from './admin/membership.routes';

// 客户端路由
import clientAuthRoutes from './client/auth.routes';
import clientMembershipRoutes from './client/membership.routes';
import clientChatAccountRoutes from './client/chat-account.routes';
import clientTranslationRouteRoutes from './client/translation-route.routes';
import clientTranslationRoutes from './client/translation.routes';
import clientPointsRoutes from './client/points.routes';

/**
 * 注册所有路由
 */
export const registerRoutes = (app: Koa): void => {
  // 注册管理端路由
  app.use(adminAuthRoutes.routes()).use(adminAuthRoutes.allowedMethods());
  app.use(adminMembershipRoutes.routes()).use(adminMembershipRoutes.allowedMethods());

  // 注册客户端路由
  app.use(clientAuthRoutes.routes()).use(clientAuthRoutes.allowedMethods());
  app.use(clientMembershipRoutes.routes()).use(clientMembershipRoutes.allowedMethods());
  app.use(clientChatAccountRoutes.routes()).use(clientChatAccountRoutes.allowedMethods());
  app.use(clientTranslationRouteRoutes.routes()).use(clientTranslationRouteRoutes.allowedMethods());
  app.use(clientTranslationRoutes.routes()).use(clientTranslationRoutes.allowedMethods());
  app.use(clientPointsRoutes.routes()).use(clientPointsRoutes.allowedMethods());

  // 404 处理
  app.use(async (ctx) => {
    ctx.status = 404;
    ctx.body = {
      code: 404,
      message: '请求的资源不存在',
    };
  });
};
