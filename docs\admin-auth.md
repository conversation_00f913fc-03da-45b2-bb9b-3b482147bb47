# 管理端认证模块 API 文档

## 概述

管理端认证模块提供管理员账户的认证相关功能，包括登录、验证码获取、密码管理等。

**基础路径:** `/api/admin/auth`

## 接口列表

### 1. 获取验证码

获取图片验证码，用于登录时的安全验证。

- **URL:** `/api/admin/auth/captcha`
- **方法:** `GET`
- **权限:** 公开接口
- **查询参数:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| type | string | 否 | 验证码类型，默认为图片验证码 |
| width | string | 否 | 验证码图片宽度 |
| height | string | 否 | 验证码图片高度 |

- **成功响应:**

```json
{
  "code": 200,
  "data": {
    "id": "captcha_123456",
    "dataUrl": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
  }
}
```

### 2. 管理员登录

管理员账户登录接口，需要提供邮箱、密码和验证码。

- **URL:** `/api/admin/auth/login`
- **方法:** `POST`
- **权限:** 公开接口
- **请求体:**

```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "captchaId": "captcha_123456",
  "captchaAnswer": "ABCD"
}
```

**请求参数说明:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| email | string | 是 | 管理员邮箱 |
| password | string | 是 | 管理员密码 |
| captchaId | string | 是 | 验证码ID |
| captchaAnswer | string | 是 | 验证码答案 |

- **成功响应:**

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "account": {
      "id": "admin_001",
      "name": "系统管理员",
      "email": "<EMAIL>",
      "isSystemAdmin": true,
      "tenantId": "default",
      "isActive": true,
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  }
}
```

- **错误响应:**

```json
{
  "code": 400,
  "message": "验证码错误或已过期"
}
```

```json
{
  "code": 400,
  "message": "邮箱或密码错误"
}
```

### 3. 忘记密码

发送密码重置链接到管理员邮箱。

- **URL:** `/api/admin/auth/forgot-password`
- **方法:** `POST`
- **权限:** 公开接口
- **请求体:**

```json
{
  "email": "<EMAIL>"
}
```

**请求参数说明:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| email | string | 是 | 管理员邮箱地址 |

- **成功响应:**

```json
{
  "code": 200,
  "message": "重置密码链接已发送至您的邮箱",
  "data": true
}
```

### 4. 重置密码

通过重置令牌设置新密码。

- **URL:** `/api/admin/auth/reset-password`
- **方法:** `POST`
- **权限:** 公开接口
- **请求体:**

```json
{
  "token": "reset_token_123456",
  "password": "newPassword123"
}
```

**请求参数说明:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| token | string | 是 | 密码重置令牌 |
| password | string | 是 | 新密码 |

- **成功响应:**

```json
{
  "code": 200,
  "message": "密码重置成功",
  "data": true
}
```

## 认证说明

除了公开接口外，其他管理端接口都需要在请求头中携带认证令牌：

```
Authorization: Bearer {token}
```

## 错误码说明

| 错误码 | 描述 |
|--------|------|
| 400 | 请求参数错误或验证失败 |
| 401 | 未授权或认证失败 |
| 403 | 权限不足，非管理员账户 |
| 500 | 服务器内部错误 |

## 注意事项

1. 管理员登录需要验证码验证，验证码有效期为5分钟
2. 登录成功后返回的JWT令牌需要妥善保存
3. 密码重置令牌有效期为30分钟
4. 管理员账户具有系统最高权限，请谨慎使用
