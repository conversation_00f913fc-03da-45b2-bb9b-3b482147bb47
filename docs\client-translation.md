# 客户端翻译模块 API 文档

## 概述

客户端翻译模块提供翻译服务功能，包括文本翻译、图片翻译、获取支持的语言列表、获取可用翻译路由等。

**基础路径:** `/api/client/translation`

## 认证说明

所有接口都需要在请求头中携带认证令牌：`Authorization: Bearer {token}`

## 接口列表

### 1. 文本翻译

执行文本翻译操作。

- **URL:** `/api/client/translation/translate`
- **方法:** `POST`
- **权限:** 需要用户认证
- **请求体:**

```json
{
  "text": "Hello, world!",
  "from": "en",
  "to": "zh",
  "vendor": "baidu"
}
```

**请求参数说明:**

| 参数名 | 类型   | 必填 | 描述                                    |
| ------ | ------ | ---- | --------------------------------------- |
| text   | string | 是   | 待翻译的文本                            |
| from   | string | 是   | 源语言代码                              |
| to     | string | 是   | 目标语言代码                            |
| vendor | string | 否   | 翻译供应商（baidu/youdao/google/deepl） |

- **成功响应:**

```json
{
  "code": 200,
  "message": "翻译成功",
  "data": {
    "text": "你好，世界！",
    "from": "en",
    "to": "zh",
    "vendor": "baidu"
  }
}
```

### 2. 图片翻译

执行图片翻译操作。

- **URL:** `/api/client/translation/translate-image`
- **方法:** `POST`
- **权限:** 需要用户认证
- **请求体:** `multipart/form-data`

**请求参数说明:**

| 参数名 | 类型   | 必填 | 描述                         |
| ------ | ------ | ---- | ---------------------------- |
| image  | file   | 是   | 图片文件                     |
| from   | string | 是   | 源语言代码                   |
| to     | string | 是   | 目标语言代码                 |
| vendor | string | 否   | 翻译供应商（youdao/alibaba） |

- **成功响应:**

```json
{
  "code": 200,
  "message": "图片翻译成功",
  "data": {
    "translatedText": "翻译后的文本内容",
    "from": "en",
    "to": "zh",
    "vendor": "youdao"
  }
}
```

### 3. 获取支持的语言列表

获取指定翻译供应商支持的语言列表。

- **URL:** `/api/client/translation/languages`
- **方法:** `GET`
- **权限:** 需要用户认证
- **查询参数:**

| 参数名 | 类型   | 必填 | 描述                                    |
| ------ | ------ | ---- | --------------------------------------- |
| vendor | string | 是   | 翻译供应商（baidu/youdao/google/deepl） |
| type   | string | 否   | 语言类型（source/target），默认为source |

- **成功响应:**

```json
{
  "code": 200,
  "message": "获取语言列表成功",
  "data": {
    "zh": "中文",
    "en": "英语",
    "ja": "日语",
    "ko": "韩语",
    "fr": "法语",
    "de": "德语",
    "es": "西班牙语",
    "ru": "俄语"
  }
}
```

### 4. 获取可用翻译路由

获取当前用户可用的翻译路由信息。

- **URL:** `/api/client/translation/routes`
- **方法:** `GET`
- **权限:** 需要用户认证
- **成功响应:**

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "baidu": [
      {
        "id": "route_001",
        "type": "text",
        "vendor": "baidu",
        "isActive": true,
        "sort": 1,
        "accountId": "user_001",
        "createdAt": "2023-01-01T00:00:00.000Z"
      }
    ],
    "youdao": [
      {
        "id": "route_002",
        "type": "text",
        "vendor": "youdao",
        "isActive": true,
        "sort": 1,
        "accountId": "user_001",
        "createdAt": "2023-01-01T00:00:00.000Z"
      },
      {
        "id": "route_003",
        "type": "image",
        "vendor": "youdao",
        "isActive": true,
        "sort": 2,
        "accountId": "user_001",
        "createdAt": "2023-01-01T00:00:00.000Z"
      }
    ]
  }
}
```

## 翻译供应商说明

| 供应商       | 支持类型           | 描述               |
| ------------ | ------------------ | ------------------ |
| baidu        | 文本翻译           | 百度翻译API        |
| youdao       | 文本翻译、图片翻译 | 有道翻译API        |
| alibaba      | 图片翻译           | 阿里云机器翻译API  |
| ~~google~~ | ~~文本翻译~~     | ~~谷歌翻译API~~  |
| ~~deepl~~  | ~~文本翻译~~     | ~~DeepL翻译API~~ |

## 常用语言代码

| 代码 | 语言     |
| ---- | -------- |
| zh   | 中文     |
| en   | 英语     |
| ja   | 日语     |
| ko   | 韩语     |
| fr   | 法语     |
| de   | 德语     |
| es   | 西班牙语 |
| ru   | 俄语     |
| ar   | 阿拉伯语 |
| th   | 泰语     |

## 错误码说明

| 错误码 | 描述                         |
| ------ | ---------------------------- |
| 400    | 请求参数错误或验证失败       |
| 401    | 未授权或认证失败             |
| 403    | 权限不足或翻译配额不足       |
| 404    | 翻译路由不存在               |
| 500    | 服务器内部错误或翻译服务异常 |

## 注意事项

1. 翻译功能需要配置相应的翻译路由才能使用
2. 不同供应商支持的语言可能不同
3. 图片翻译支持有道翻译和阿里云翻译
4. 翻译结果的准确性取决于供应商的服务质量
5. 图片文件大小限制为5MB，支持格式：jpg、png、gif、bmp
6. 文本长度限制为5000字符
7. 翻译服务可能受到供应商API配额限制
