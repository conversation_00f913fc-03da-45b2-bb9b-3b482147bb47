# 客户端聊天账户模块 API 文档

## 概述

客户端聊天账户模块提供聊天账户的管理功能，包括创建、更新、删除、查询聊天账户等操作。

**基础路径:** `/api/client/chat`

## 认证说明

所有接口都需要在请求头中携带认证令牌：`Authorization: Bearer {token}`

## 接口列表

### 1. 创建聊天账户

创建新的聊天账户。

- **URL:** `/api/client/chat/accounts`
- **方法:** `POST`
- **权限:** 需要用户认证
- **请求体:**

```json
{
  "platform": "wechat",
  "accountId": "wx_user_001",
  "nickname": "微信用户1",
  "avatar": "https://example.com/avatar.jpg"
}
```

**请求参数说明:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| platform | string | 是 | 平台类型（wechat/qq/telegram等） |
| accountId | string | 否 | 第三方平台账户ID |
| nickname | string | 否 | 昵称 |
| avatar | string | 否 | 头像URL |

- **成功响应:**

```json
{
  "code": 200,
  "message": "创建聊天账号成功",
  "data": {
    "id": "chat_001",
    "platform": "wechat",
    "accountId": "wx_user_001",
    "nickname": "微信用户1",
    "avatar": "https://example.com/avatar.jpg",
    "tenantId": "tenant_001",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### 2. 获取聊天账户列表

获取当前用户的所有聊天账户。

- **URL:** `/api/client/chat/accounts`
- **方法:** `GET`
- **权限:** 需要用户认证

- **成功响应:**

```json
{
  "code": 200,
  "message": "获取聊天账号列表成功",
  "data": [
    {
      "id": "chat_001",
      "platform": "wechat",
      "accountId": "wx_user_001",
      "nickname": "微信用户1",
      "avatar": "https://example.com/avatar.jpg",
      "tenantId": "tenant_001",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    },
    {
      "id": "chat_002",
      "platform": "qq",
      "accountId": "qq_user_001",
      "nickname": "QQ用户1",
      "avatar": "https://example.com/qq-avatar.jpg",
      "tenantId": "tenant_001",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

### 3. 获取单个聊天账户

获取指定聊天账户的详细信息。

- **URL:** `/api/client/chat/accounts/{id}`
- **方法:** `GET`
- **权限:** 需要用户认证
- **路径参数:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | string | 是 | 聊天账户ID |

- **成功响应:**

```json
{
  "code": 200,
  "message": "获取聊天账号成功",
  "data": {
    "id": "chat_001",
    "platform": "wechat",
    "accountId": "wx_user_001",
    "nickname": "微信用户1",
    "avatar": "https://example.com/avatar.jpg",
    "tenantId": "tenant_001",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### 4. 更新聊天账户

更新指定聊天账户的信息。

- **URL:** `/api/client/chat/accounts/{id}`
- **方法:** `PUT`
- **权限:** 需要用户认证
- **路径参数:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | string | 是 | 聊天账户ID |

- **请求体:**

```json
{
  "nickname": "新昵称",
  "avatar": "https://example.com/new-avatar.jpg"
}
```

**请求参数说明:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| accountId | string | 否 | 第三方平台账户ID |
| nickname | string | 否 | 昵称 |
| avatar | string | 否 | 头像URL |

- **成功响应:**

```json
{
  "code": 200,
  "message": "更新聊天账号成功",
  "data": {
    "id": "chat_001",
    "platform": "wechat",
    "accountId": "wx_user_001",
    "nickname": "新昵称",
    "avatar": "https://example.com/new-avatar.jpg",
    "tenantId": "tenant_001",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-02T00:00:00.000Z"
  }
}
```

### 5. 删除聊天账户

删除指定的聊天账户。

- **URL:** `/api/client/chat/accounts/{id}`
- **方法:** `DELETE`
- **权限:** 需要用户认证
- **路径参数:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | string | 是 | 聊天账户ID |

- **成功响应:**

```json
{
  "code": 200,
  "message": "删除聊天账号成功"
}
```

## 平台类型说明

| 平台类型 | 描述 |
|----------|------|
| wechat | 微信 |
| qq | QQ |
| telegram | Telegram |
| discord | Discord |
| slack | Slack |

## 错误码说明

| 错误码 | 描述 |
|--------|------|
| 400 | 请求参数错误或验证失败 |
| 401 | 未授权或认证失败 |
| 403 | 权限不足 |
| 404 | 聊天账户不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 每个用户可以创建多个聊天账户
2. 聊天账户与用户绑定，只能管理自己的账户
3. 删除聊天账户是永久性操作，请谨慎操作
4. 平台类型一旦创建不可修改
5. 头像URL需要是有效的图片链接
6. 昵称长度限制为1-50个字符
