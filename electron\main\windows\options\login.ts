import { PRELOAD } from '../../config/config'

export default {
    title: '登录',
    width: 340,
    height: 460,
    // 窗口不可调整大小
    resizable: false,
    // 窗口不能最小化
    minimizable: false,
    // 窗口不能最大化
    maximizable: false,
    // 窗口不能进入全屏状态
    fullscreenable: false,
    // 窗口不能关闭
    closable: true,
    transparent: true,//透明窗口
    backgroundColor: '#00000000',//窗口底色为透明色
    frame: false,//是否无边框
    webPreferences: {
        preload: PRELOAD,
        // Warning: Enable nodeIntegration and disable contextIsolation is not secure in production
        // Consider using contextBridge.exposeInMainWorld
        // Read more on https://www.electronjs.org/docs/latest/tutorial/context-isolation
        nodeIntegration: true,
    },
}