# 文档翻译页面

## 📄 功能概述

文档翻译页面基于图片翻译页面的排版设计，提供了完整的文档翻译功能，支持多种文档格式的高质量翻译。

## 🎯 主要功能

### 1. 文档上传
- **支持格式**: DOC, DOCX, PDF, TXT, HTML, XML, PPT, PPTX, XLS, XLSX
- **文件大小**: 最大支持 50MB
- **上传方式**: 点击选择或拖拽上传
- **格式验证**: 自动验证文件类型和大小

### 2. 翻译配置
- **翻译线路**: 目前支持百度翻译
- **语言选择**: 支持200+种语言互译
- **垂直领域**: 支持通用领域翻译
- **输出格式**: 可选择多种输出格式
- **文件命名**: 支持自定义文件名前缀

### 3. 异步翻译处理
- **任务创建**: 创建文档翻译任务
- **状态查询**: 实时查询翻译进度
- **自动查询**: 每5秒自动查询状态
- **结果下载**: 翻译完成后直接下载

## 🔧 技术特性

### 界面设计
- **响应式布局**: 基于Tailwind CSS的响应式设计
- **三栏布局**: 原文档 → 翻译按钮 → 翻译结果
- **状态指示**: 清晰的状态标签和进度提示
- **交互友好**: 拖拽上传、一键下载等便捷操作

### 功能实现
- **文件处理**: 支持多种文档格式的验证和处理
- **Base64编码**: 安全的文件传输方式
- **异步任务**: 完整的异步翻译流程
- **错误处理**: 完善的错误提示和处理机制

## 📋 使用流程

### 1. 选择文档
```
点击上传区域 → 选择文档文件 → 自动验证格式和大小
或
拖拽文档文件到上传区域 → 自动处理
```

### 2. 配置翻译参数
```
选择翻译线路: 百度翻译
设置源语言: 自动检测或手动选择
设置目标语言: 选择目标语言
选择垂直领域: 通用领域（可选）
设置输出格式: 根据原文档格式选择（可选）
设置文件名前缀: 自定义前缀（可选）
```

### 3. 执行翻译
```
点击翻译按钮 → 创建翻译任务 → 获取任务ID → 开始状态查询
```

### 4. 查询状态
```
手动查询: 点击"查询状态"按钮
自动查询: 点击"自动查询"按钮，每5秒查询一次
状态类型: NotStarted → Running → Succeeded/Failed/Expired
```

### 5. 下载结果
```
翻译完成 → 显示结果文件列表 → 点击下载按钮 → 保存到本地
```

## 🎨 界面元素

### 翻译设置区域
- 翻译线路选择器
- 源语言/目标语言选择器
- 垂直领域选择器
- 输出格式多选器
- 文件名前缀输入框

### 原文档区域
- 文档上传区域（拖拽支持）
- 文档信息显示（文件名、大小、格式）
- 文档图标预览
- 清除按钮

### 翻译按钮区域
- 圆形翻译按钮
- 加载状态指示
- 状态文字提示

### 翻译结果区域
- 任务信息显示
- 状态查询按钮
- 自动查询控制
- 翻译进度显示
- 结果文件下载

## 📊 状态管理

### 翻译状态
- **NotStarted**: 任务已创建，等待开始
- **Running**: 翻译正在进行中
- **Succeeded**: 翻译完成，可下载结果
- **Failed**: 翻译失败，显示错误信息
- **Expired**: 任务已过期

### 状态标签颜色
- **成功**: 绿色 (success)
- **失败**: 红色 (danger)
- **进行中**: 蓝色 (primary)
- **等待**: 橙色 (warning)
- **过期**: 灰色 (info)

## 🔄 API集成

### 文档翻译API
```typescript
translateDocumentAPI({
    documentBase64: string,
    from: string,
    to: string,
    format: string,
    filename: string,
    vendor: VendorType.BAIDU,
    domain?: string,
    outputFormats?: string[],
    filenamePrefix?: string
})
```

### 状态查询API
```typescript
queryDocumentTranslationAPI({
    taskId: string,
    vendor: VendorType.BAIDU
})
```

## 🛠️ 开发说明

### 组件结构
```
tranDoc/
├── index.vue          # 主组件文件
└── README.md         # 功能说明文档
```

### 依赖组件
- Element Plus UI组件库
- Vue 3 Composition API
- Tailwind CSS样式框架
- 翻译API服务

### 核心函数
- `handleFileSelect()`: 文件选择处理
- `handleDrop()`: 拖拽上传处理
- `handleTranslate()`: 执行翻译
- `queryStatus()`: 查询翻译状态
- `toggleAutoQuery()`: 切换自动查询
- `downloadFile()`: 下载翻译结果

## 🔍 错误处理

### 文件验证
- 文件格式检查
- 文件大小限制
- 文件类型验证

### API错误
- 网络连接错误
- 服务器响应错误
- 参数验证错误

### 用户提示
- 成功操作提示
- 错误信息提示
- 警告信息提示
- 进度状态提示

## 📈 性能优化

### 文件处理
- 大文件分片处理
- Base64编码优化
- 内存使用控制

### 状态查询
- 智能查询间隔
- 自动停止机制
- 缓存状态信息

### 用户体验
- 加载状态指示
- 操作反馈提示
- 错误恢复机制

---

**开发时间**: 2025-07-29
**版本**: v1.0
**维护人员**: AI Assistant
