# 图片翻译功能使用说明

## 功能概述

图片翻译功能允许用户上传图片并将图片中的文字翻译成目标语言。支持有道翻译和阿里云翻译两种服务。

## 界面布局

### 1. 翻译设置区域（顶部）
- **翻译供应商选择**: 支持有道翻译和阿里云翻译
- **源语言选择**: 图片中文字的原始语言
- **目标语言选择**: 要翻译成的目标语言

### 2. 主要内容区域（三栏布局）

#### 左侧 - 原图区域
- **图片上传**: 支持点击选择或拖拽上传
- **支持格式**: PNG, JPG, GIF, BMP
- **文件大小限制**: 最大 5MB
- **图片预览**: 上传后显示图片预览
- **清除功能**: 可以清除已选择的图片

#### 中间 - 翻译按钮
- **圆形翻译按钮**: 点击开始翻译
- **状态显示**: 显示"开始翻译"或"翻译中..."
- **按钮状态**: 只有在选择图片和完善配置后才能点击

#### 右侧 - 翻译结果区域
- **结果显示**: 以文本形式显示翻译结果
- **复制功能**: 一键复制翻译结果
- **状态提示**: 显示等待、翻译中、结果等不同状态

## 使用步骤

### 1. 配置翻译设置
1. 选择翻译供应商（有道翻译或阿里云翻译）
2. 选择源语言（图片中文字的语言）
3. 选择目标语言（要翻译成的语言）

### 2. 上传图片
**方式一：点击上传**
1. 点击"点击选择图片或拖拽到此处"区域
2. 在文件选择器中选择图片文件

**方式二：拖拽上传**
1. 直接将图片文件拖拽到上传区域
2. 系统会自动识别并加载图片

### 3. 执行翻译
1. 确认图片和翻译配置无误
2. 点击中间的圆形翻译按钮
3. 等待翻译完成

### 4. 查看和使用结果
1. 翻译完成后，结果会显示在右侧区域
2. 点击"复制"按钮可以复制翻译结果
3. 如需重新翻译，可以修改配置后再次点击翻译按钮

## 支持的语言

### 有道翻译支持的语言
- 中文、英语、日语、韩语、法语、西班牙语、葡萄牙语、意大利语、俄语、越南语、德语、阿拉伯语、印尼语、泰语等

### 阿里云翻译支持的语言
- 中文、英语、日语、韩语、法语、西班牙语、意大利语、德语、土耳其语、俄语、葡萄牙语、越南语、印尼语、泰语、马来语、阿拉伯语、印地语

## 注意事项

### 图片要求
1. **文件格式**: 仅支持 PNG、JPG、GIF、BMP 格式
2. **文件大小**: 不能超过 5MB
3. **图片质量**: 建议使用清晰的图片以获得更好的翻译效果
4. **文字清晰度**: 图片中的文字应该清晰可见

### 翻译配置
1. **供应商选择**: 不同供应商支持的语言可能不同
2. **语言配置**: 必须选择正确的源语言和目标语言
3. **网络连接**: 需要稳定的网络连接

### 使用限制
1. **翻译路由**: 需要先在系统中配置相应的翻译路由
2. **API配额**: 翻译服务可能有使用配额限制
3. **并发限制**: 避免同时进行多个翻译任务

## 错误处理

### 常见错误及解决方案

| 错误信息 | 可能原因 | 解决方案 |
|----------|----------|----------|
| 请先选择图片 | 未上传图片 | 上传符合要求的图片文件 |
| 请完善翻译配置 | 翻译配置不完整 | 检查供应商、源语言、目标语言是否都已选择 |
| 图片文件大小不能超过 5MB | 文件过大 | 压缩图片或选择更小的图片 |
| 不支持的图片格式 | 文件格式不支持 | 使用 PNG、JPG、GIF 或 BMP 格式的图片 |
| 获取语言列表失败 | 网络或服务器问题 | 检查网络连接，稍后重试 |
| 翻译失败 | 服务器错误或配置问题 | 检查翻译路由配置，稍后重试 |

### 调试建议
1. **检查控制台**: 查看浏览器控制台的错误信息
2. **网络检查**: 确认网络连接正常
3. **配置验证**: 确认翻译路由配置正确
4. **文件检查**: 确认图片文件符合要求

## 技术实现

### 核心功能
- **文件上传**: 支持点击选择和拖拽上传
- **图片预览**: 实时显示上传的图片
- **Base64转换**: 将图片转换为Base64格式传输
- **API调用**: 调用后端翻译接口
- **结果展示**: 格式化显示翻译结果
- **复制功能**: 支持一键复制结果

### 技术栈
- **Vue 3**: 使用 Composition API
- **TypeScript**: 类型安全
- **Element Plus**: UI 组件库
- **Tailwind CSS**: 样式框架
- **Axios**: HTTP 请求库

## 更新日志

### v1.0.0
- ✅ 基础图片翻译功能
- ✅ 支持有道翻译和阿里云翻译
- ✅ 拖拽上传功能
- ✅ 图片预览功能
- ✅ 翻译结果复制功能
- ✅ 完整的错误处理
- ✅ 响应式设计

## 后续计划

### 功能增强
- [ ] 批量图片翻译
- [ ] 翻译历史记录
- [ ] 结果导出功能
- [ ] 更多翻译供应商支持
- [ ] 图片编辑功能（裁剪、旋转等）

### 性能优化
- [ ] 图片压缩优化
- [ ] 缓存机制
- [ ] 并发控制
- [ ] 进度显示优化
