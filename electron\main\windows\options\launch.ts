import { PRELOAD } from '../../config/config';

export default {
    title: '登录',
    width: 500,
    height: 300,
    // 创建无边框窗口
    frame: false,
    // 创建一个完全透明的窗口
    transparent: true,
    // 窗口可移动
    movable: true,
    // 窗口可调整大小
    resizable: false,
    // 窗口不能最小化
    minimizable: false,
    // 窗口不能最大化
    maximizable: false,
    // 窗口不能进入全屏状态
    fullscreenable: false,
    // 窗口不能关闭
    closable: false,
    //窗口底色为透明色
    backgroundColor: '#00000000',
    webPreferences: {
        preload: PRELOAD,
        // Warning: Enable nodeIntegration and disable contextIsolation is not secure in production
        // Consider using contextBridge.exposeInMainWorld
        // Read more on https://www.electronjs.org/docs/latest/tutorial/context-isolation
        nodeIntegration: true,
    },
}