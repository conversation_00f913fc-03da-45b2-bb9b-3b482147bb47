<template>
    <div class="w-[42px] h-full flex flex-col flex-shrink-0">
        <div class="w-full flex flex-col items-center">
            <Menu class="my-1" :icon="SmartHome" size="20" :active="routeName === 'index'" @click="to('index')"></Menu>
        </div>
        <div class="flex-grow overflow-y-auto scrollbar-hide flex flex-col items-center">
            <Menu class="my-1" v-for="menu in permssionRoutes" :key="menu.name" :active="routeName === menu.name"
                :size="menuSize" :icon="menu.meta.icon" @click="to(menu.name)"></Menu>
        </div>
        <div class="w-full flex flex-col items-center">
            <!-- <Menu :icon="Apps" :size="menuSize"></Menu> -->
            <Menu class="my-1" :icon="SettingsOutline" :size="menuSize" tooltip="设置"
                :active="routePath.startsWith('/setting')" @click="to('setting')"></Menu>
            <Menu class="my-1" :icon="LogoutOutlined" :size="menuSize" @click="logout" tooltip="退出登录"></Menu>
        </div>
    </div>
</template>
<script setup lang="ts">
import { LogoutOutlined } from '@vicons/antd';
import { SettingsOutline } from '@vicons/ionicons5';
import { Apps, SmartHome } from '@vicons/tabler';
import Menu from '@/components/Menu/index.vue';
import { useUserAuthStore } from '../../stores/user-auth';
import { useUserInfoStore } from '../../stores/user-info';
import { permssionRoutes } from '../../router/perm'
import router from '../../router';
import { useRoute } from 'vue-router';
import { computed } from 'vue';
const userAuthStore = useUserAuthStore();
const userInfoStore = useUserInfoStore();
const route = useRoute()
const menuSize = '18';
const routeName = computed(() => route.name);
const routePath = computed(() => route.path);
const to = (name: string) => {
    router.push({ name })
}
const logout = () => {
    userAuthStore.logout();
}
</script>

<style>
/* 自定义滚动条隐藏 */
.scrollbar-hide::-webkit-scrollbar {
    display: none;
}
</style>