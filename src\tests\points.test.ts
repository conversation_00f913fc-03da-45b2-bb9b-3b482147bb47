// 积分系统测试文件

// 模拟API调用
const mockProducts = [
  {
    id: '1',
    name: '基础套餐',
    description: '100积分',
    points: 100,
    price: 1000, // 单位：分
    discountRate: 1.0,
    bonusPoints: 0,
    isActive: true,
    sort: 1,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  },
  {
    id: '2',
    name: '高级套餐',
    description: '500积分',
    points: 500,
    price: 4500, // 单位：分
    discountRate: 0.9,
    bonusPoints: 50,
    isActive: true,
    sort: 2,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  }
];

const mockBalance = 120;

const mockOrder = {
  id: 'order123',
  orderNo: 'PO1234567890123',
  clientId: 'client123',
  productId: '1',
  originalPrice: 1000,
  discountPrice: 1000,
  actualPrice: 1000,
  points: 100,
  bonusPoints: 0,
  status: 'pending',
  paymentMethod: '',
  transactionId: '',
  paidAt: '',
  expiredAt: '',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z'
};

const mockPayment = {
  paymentUrl: 'https://z-pay.cn/pay/PO1234567890123'
};

// 测试API函数
export const testPointsAPI = () => {
  console.log('Testing Points API...');
  
  // 测试获取积分商品列表
  console.log('Products:', mockProducts);
  
  // 测试获取用户积分余额
  console.log('Balance:', mockBalance);
  
  // 测试创建订单
  console.log('Created Order:', mockOrder);
  
  // 测试创建支付链接
  console.log('Payment URL:', mockPayment.paymentUrl);
  
  console.log('All tests passed!');
};