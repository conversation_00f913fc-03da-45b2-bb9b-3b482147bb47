import request from '../utils/request'

// 积分商品接口
export type PointsProduct = {
  id: string
  name: string
  description: string
  points: number
  price: number
  discountRate: number
  bonusPoints: number
  isActive: boolean
  sort: number
  createdAt: string
  updatedAt: string
}

// 积分订单接口
export type PointsOrder = {
  id: string
  orderNo: string
  clientId: string
  productId: string
  originalPrice: number
  discountPrice: number
  actualPrice: number
  points: number
  bonusPoints: number
  status: 'pending' | 'paid' | 'cancelled' | 'expired'
  paymentMethod: string
  transactionId: string
  paidAt: string
  expiredAt: string
  createdAt: string
  updatedAt: string
}

// 积分流水接口
export type PointsTransaction = {
  id: string
  clientId: string
  type: 'recharge' | 'consume' | 'refund' | 'bonus' | 'admin_adjust'
  amount: number
  balance: number
  description: string
  referenceType: 'order' | 'translation' | 'admin'
  referenceId: string
  metadata: Record<string, any>
  createdAt: string
}

// 获取积分商品列表
export function getPointsProductsAPI() {
  return request<PointsProduct[]>({
    url: '/client/points/products',
    method: 'GET'
  })
}

// 获取用户积分余额
export function getPointsBalanceAPI() {
  return request<{ balance: number }>({
    url: '/client/points/balance',
    method: 'GET'
  })
}

// 获取积分流水记录
export function getPointsTransactionsAPI(params: { page: number; limit: number }) {
  return request<PointsTransaction[]>({
    url: '/client/points/transactions',
    method: 'GET',
    params
  })
}

// 创建积分订单
export function createPointsOrderAPI(data: { productId: string }) {
  return request<PointsOrder>({
    url: '/client/points/orders',
    method: 'POST',
    data
  })
}

// 创建支付链接
export function createPaymentAPI(data: { orderNo: string; paymentMethod: string }) {
  return request<{ paymentUrl: string }>({
    url: '/client/points/payment',
    method: 'POST',
    data
  })
}

// 获取用户订单列表
export function getPointsOrdersAPI(params: { page: number; limit: number }) {
  return request<PointsOrder[]>({
    url: '/client/points/orders',
    method: 'GET',
    params
  })
}