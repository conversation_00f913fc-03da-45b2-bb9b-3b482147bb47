<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百度文档翻译功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-section h2 {
            color: #2c3e50;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            min-height: 100px;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.loading {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .result.info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .test-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .test-info h3 {
            margin-top: 0;
            color: #0066cc;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✓ ";
            color: #27ae60;
            font-weight: bold;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-notstarted { background-color: #ffc107; color: #212529; }
        .status-running { background-color: #17a2b8; color: white; }
        .status-succeeded { background-color: #28a745; color: white; }
        .status-failed { background-color: #dc3545; color: white; }
        .status-expired { background-color: #6c757d; color: white; }
        .file-list {
            margin-top: 10px;
        }
        .file-item {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
        }
        .file-item a {
            color: #007bff;
            text-decoration: none;
        }
        .file-item a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📄 百度文档翻译功能测试</h1>
        
        <div class="test-info">
            <h3>📋 测试说明</h3>
            <p>本页面用于测试新接入的百度文档翻译功能。百度文档翻译结合高还原度的文档解析和机器翻译技术，提供多格式、多语种、高质量的文档翻译服务。</p>
            
            <h4>🎯 支持的功能特性：</h4>
            <ul class="feature-list">
                <li>支持多种文档格式：Word、PPT、Excel、HTML、XML、TXT、PDF等</li>
                <li>支持200+语种互译</li>
                <li>高还原度的文档解析</li>
                <li>异步翻译任务处理</li>
                <li>支持垂直领域翻译</li>
                <li>支持自定义输出格式</li>
            </ul>
            
            <h4>📝 测试步骤：</h4>
            <ol>
                <li>配置百度翻译API密钥</li>
                <li>选择源语言和目标语言</li>
                <li>上传文档文件</li>
                <li>设置翻译参数（可选）</li>
                <li>创建翻译任务</li>
                <li>查询翻译状态</li>
                <li>下载翻译结果</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>🔧 API配置测试</h2>
            <div class="form-group">
                <label for="apiKey">百度API Key:</label>
                <input type="text" id="apiKey" placeholder="请输入百度翻译API Key">
            </div>
            <div class="form-group">
                <label for="secretKey">百度Secret Key:</label>
                <input type="password" id="secretKey" placeholder="请输入百度翻译Secret Key">
            </div>
            <button onclick="testApiConfig()">测试API配置</button>
            <div id="apiResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>📄 文档翻译测试</h2>
            <div class="form-group">
                <label for="sourceLanguage">源语言:</label>
                <select id="sourceLanguage">
                    <option value="auto">自动检测</option>
                    <option value="zh">中文(简体)</option>
                    <option value="cht">中文(繁体)</option>
                    <option value="en">英语</option>
                    <option value="jp">日语</option>
                    <option value="kor">韩语</option>
                    <option value="fra">法语</option>
                    <option value="de">德语</option>
                    <option value="spa">西班牙语</option>
                    <option value="ru">俄语</option>
                </select>
            </div>
            <div class="form-group">
                <label for="targetLanguage">目标语言:</label>
                <select id="targetLanguage">
                    <option value="en">英语</option>
                    <option value="zh">中文(简体)</option>
                    <option value="cht">中文(繁体)</option>
                    <option value="jp">日语</option>
                    <option value="kor">韩语</option>
                    <option value="fra">法语</option>
                    <option value="de">德语</option>
                    <option value="spa">西班牙语</option>
                    <option value="ru">俄语</option>
                </select>
            </div>
            <div class="form-group">
                <label for="documentFile">选择文档:</label>
                <input type="file" id="documentFile" accept=".doc,.docx,.pdf,.txt,.html,.xml,.ppt,.pptx,.xls,.xlsx">
            </div>
            <div class="form-group">
                <label for="domain">垂直领域 (可选):</label>
                <select id="domain">
                    <option value="">通用领域</option>
                    <option value="general">通用领域</option>
                </select>
            </div>
            <div class="form-group">
                <label for="outputFormats">输出格式 (可选):</label>
                <select id="outputFormats" multiple>
                    <option value="docx">DOCX</option>
                    <option value="pdf">PDF</option>
                    <option value="txt">TXT</option>
                    <option value="html">HTML</option>
                    <option value="xml">XML</option>
                    <option value="pptx">PPTX</option>
                    <option value="xlsx">XLSX</option>
                </select>
                <small>按住Ctrl键可多选</small>
            </div>
            <div class="form-group">
                <label for="filenamePrefix">文件名前缀 (可选):</label>
                <input type="text" id="filenamePrefix" placeholder="例如：translated_">
            </div>
            <button onclick="createTranslationTask()" id="createBtn">创建翻译任务</button>
            <div id="createResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>🔍 查询翻译状态</h2>
            <div class="form-group">
                <label for="taskId">任务ID:</label>
                <input type="text" id="taskId" placeholder="请输入翻译任务ID">
            </div>
            <button onclick="queryTranslationStatus()" id="queryBtn">查询状态</button>
            <button onclick="autoQuery()" id="autoQueryBtn">自动查询</button>
            <div id="queryResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>📊 支持的文档格式</h2>
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background-color: #f8f9fa;">
                        <th style="border: 1px solid #dee2e6; padding: 8px;">输入格式</th>
                        <th style="border: 1px solid #dee2e6; padding: 8px;">支持的输出格式</th>
                        <th style="border: 1px solid #dee2e6; padding: 8px;">默认输出格式</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td style="border: 1px solid #dee2e6; padding: 8px;">doc</td><td style="border: 1px solid #dee2e6; padding: 8px;">docx, pdf</td><td style="border: 1px solid #dee2e6; padding: 8px;">docx</td></tr>
                    <tr><td style="border: 1px solid #dee2e6; padding: 8px;">docx</td><td style="border: 1px solid #dee2e6; padding: 8px;">docx, pdf</td><td style="border: 1px solid #dee2e6; padding: 8px;">docx</td></tr>
                    <tr><td style="border: 1px solid #dee2e6; padding: 8px;">pdf</td><td style="border: 1px solid #dee2e6; padding: 8px;">docx, pdf</td><td style="border: 1px solid #dee2e6; padding: 8px;">docx</td></tr>
                    <tr><td style="border: 1px solid #dee2e6; padding: 8px;">txt</td><td style="border: 1px solid #dee2e6; padding: 8px;">txt</td><td style="border: 1px solid #dee2e6; padding: 8px;">txt</td></tr>
                    <tr><td style="border: 1px solid #dee2e6; padding: 8px;">html</td><td style="border: 1px solid #dee2e6; padding: 8px;">html</td><td style="border: 1px solid #dee2e6; padding: 8px;">html</td></tr>
                    <tr><td style="border: 1px solid #dee2e6; padding: 8px;">xml</td><td style="border: 1px solid #dee2e6; padding: 8px;">xml</td><td style="border: 1px solid #dee2e6; padding: 8px;">xml</td></tr>
                    <tr><td style="border: 1px solid #dee2e6; padding: 8px;">ppt</td><td style="border: 1px solid #dee2e6; padding: 8px;">pptx</td><td style="border: 1px solid #dee2e6; padding: 8px;">pptx</td></tr>
                    <tr><td style="border: 1px solid #dee2e6; padding: 8px;">pptx</td><td style="border: 1px solid #dee2e6; padding: 8px;">pptx</td><td style="border: 1px solid #dee2e6; padding: 8px;">pptx</td></tr>
                    <tr><td style="border: 1px solid #dee2e6; padding: 8px;">xls</td><td style="border: 1px solid #dee2e6; padding: 8px;">xlsx</td><td style="border: 1px solid #dee2e6; padding: 8px;">xlsx</td></tr>
                    <tr><td style="border: 1px solid #dee2e6; padding: 8px;">xlsx</td><td style="border: 1px solid #dee2e6; padding: 8px;">xlsx</td><td style="border: 1px solid #dee2e6; padding: 8px;">xlsx</td></tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        let autoQueryInterval = null;

        // 测试API配置
        async function testApiConfig() {
            const apiKey = document.getElementById('apiKey').value;
            const secretKey = document.getElementById('secretKey').value;
            const resultDiv = document.getElementById('apiResult');
            
            if (!apiKey || !secretKey) {
                showResult(resultDiv, 'error', '请输入API Key和Secret Key');
                return;
            }
            
            showResult(resultDiv, 'loading', '正在测试API配置...');
            
            try {
                // 测试获取access_token
                const response = await fetch('https://aip.baidubce.com/oauth/2.0/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        grant_type: 'client_credentials',
                        client_id: apiKey,
                        client_secret: secretKey
                    })
                });
                
                const data = await response.json();
                
                if (data.access_token) {
                    showResult(resultDiv, 'success', `API配置正确！Access Token: ${data.access_token.substring(0, 20)}...`);
                } else {
                    showResult(resultDiv, 'error', `API配置错误：${data.error_description || data.error || '未知错误'}`);
                }
            } catch (error) {
                showResult(resultDiv, 'error', `网络错误：${error.message}`);
            }
        }

        // 创建翻译任务
        async function createTranslationTask() {
            const apiKey = document.getElementById('apiKey').value;
            const secretKey = document.getElementById('secretKey').value;
            const sourceLanguage = document.getElementById('sourceLanguage').value;
            const targetLanguage = document.getElementById('targetLanguage').value;
            const fileInput = document.getElementById('documentFile');
            const domain = document.getElementById('domain').value;
            const outputFormatsSelect = document.getElementById('outputFormats');
            const filenamePrefix = document.getElementById('filenamePrefix').value;
            const resultDiv = document.getElementById('createResult');
            const createBtn = document.getElementById('createBtn');
            
            if (!apiKey || !secretKey) {
                showResult(resultDiv, 'error', '请先配置API Key和Secret Key');
                return;
            }
            
            if (!fileInput.files || !fileInput.files[0]) {
                showResult(resultDiv, 'error', '请选择要翻译的文档');
                return;
            }
            
            const file = fileInput.files[0];
            const format = getFileFormat(file.name);
            
            if (!format) {
                showResult(resultDiv, 'error', '不支持的文件格式');
                return;
            }
            
            createBtn.disabled = true;
            showResult(resultDiv, 'loading', '正在创建翻译任务，请稍候...');
            
            try {
                const documentBase64 = await fileToBase64(file);
                const outputFormats = Array.from(outputFormatsSelect.selectedOptions).map(option => option.value);
                
                // 模拟调用后端API
                const requestData = {
                    documentBase64: documentBase64.split(',')[1], // 去掉data:前缀
                    from: sourceLanguage,
                    to: targetLanguage,
                    format: format,
                    filename: file.name,
                    vendor: 'baidu',
                    domain: domain || undefined,
                    outputFormats: outputFormats.length > 0 ? outputFormats : undefined,
                    filenamePrefix: filenamePrefix || undefined
                };
                
                // 这里应该调用实际的后端API
                // const response = await fetch('/api/client/translation/translate-document', {
                //     method: 'POST',
                //     headers: { 'Content-Type': 'application/json' },
                //     body: JSON.stringify(requestData)
                // });
                
                // 模拟成功响应
                setTimeout(() => {
                    const mockTaskId = 'mock_' + Date.now().toString(36);
                    document.getElementById('taskId').value = mockTaskId;
                    showResult(resultDiv, 'success', 
                        `翻译任务创建成功！<br>
                        <strong>任务ID：</strong>${mockTaskId}<br>
                        <strong>文件名：</strong>${file.name}<br>
                        <strong>格式：</strong>${format}<br>
                        <strong>源语言：</strong>${sourceLanguage}<br>
                        <strong>目标语言：</strong>${targetLanguage}<br>
                        <em>注意：这是模拟结果，实际使用时需要配置正确的API密钥并连接到后端服务。</em>`
                    );
                    createBtn.disabled = false;
                }, 2000);
                
            } catch (error) {
                showResult(resultDiv, 'error', `创建翻译任务失败：${error.message}`);
                createBtn.disabled = false;
            }
        }

        // 查询翻译状态
        async function queryTranslationStatus() {
            const taskId = document.getElementById('taskId').value;
            const resultDiv = document.getElementById('queryResult');
            const queryBtn = document.getElementById('queryBtn');
            
            if (!taskId) {
                showResult(resultDiv, 'error', '请输入任务ID');
                return;
            }
            
            queryBtn.disabled = true;
            showResult(resultDiv, 'loading', '正在查询翻译状态...');
            
            try {
                // 模拟查询API调用
                setTimeout(() => {
                    const statuses = ['NotStarted', 'Running', 'Succeeded', 'Failed'];
                    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
                    
                    let statusHtml = `
                        <strong>任务ID：</strong>${taskId}<br>
                        <strong>状态：</strong><span class="status-badge status-${randomStatus.toLowerCase()}">${randomStatus}</span><br>
                        <strong>创建时间：</strong>${new Date().toLocaleString()}<br>
                    `;
                    
                    if (randomStatus === 'Succeeded') {
                        statusHtml += `
                            <strong>翻译完成！</strong><br>
                            <div class="file-list">
                                <div class="file-item">
                                    <strong>文件：</strong>translated_document.docx<br>
                                    <strong>大小：</strong>1.2MB<br>
                                    <a href="#" onclick="alert('这是模拟下载链接')">下载文件</a>
                                </div>
                            </div>
                        `;
                    } else if (randomStatus === 'Failed') {
                        statusHtml += `<strong>失败原因：</strong>文档解析失败<br>`;
                    } else if (randomStatus === 'Running') {
                        statusHtml += `<strong>进度：</strong>正在翻译中...<br>`;
                    }
                    
                    statusHtml += `<em>注意：这是模拟结果，实际使用时需要连接到后端服务。</em>`;
                    
                    showResult(resultDiv, randomStatus === 'Succeeded' ? 'success' : 
                              randomStatus === 'Failed' ? 'error' : 'info', statusHtml);
                    queryBtn.disabled = false;
                }, 1000);
                
            } catch (error) {
                showResult(resultDiv, 'error', `查询失败：${error.message}`);
                queryBtn.disabled = false;
            }
        }

        // 自动查询
        function autoQuery() {
            const autoQueryBtn = document.getElementById('autoQueryBtn');
            
            if (autoQueryInterval) {
                clearInterval(autoQueryInterval);
                autoQueryInterval = null;
                autoQueryBtn.textContent = '自动查询';
                autoQueryBtn.style.backgroundColor = '#3498db';
            } else {
                autoQueryInterval = setInterval(queryTranslationStatus, 5000);
                autoQueryBtn.textContent = '停止自动查询';
                autoQueryBtn.style.backgroundColor = '#e74c3c';
                queryTranslationStatus(); // 立即查询一次
            }
        }

        // 获取文件格式
        function getFileFormat(filename) {
            const ext = filename.split('.').pop().toLowerCase();
            const supportedFormats = ['doc', 'docx', 'pdf', 'txt', 'html', 'xml', 'ppt', 'pptx', 'xls', 'xlsx'];
            return supportedFormats.includes(ext) ? ext : null;
        }

        // 文件转base64
        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => resolve(reader.result);
                reader.onerror = error => reject(error);
            });
        }
        
        // 显示结果
        function showResult(element, type, message) {
            element.style.display = 'block';
            element.className = `result ${type}`;
            element.innerHTML = message;
        }
    </script>
</body>
</html>
