<template>
    <div class="h-full w-full bg-transparent" ref="chatViewRef">
        <slot></slot>
    </div>
</template>

<script setup lang="ts">
import { Rectangle } from 'electron';
import { ref, onMounted, onBeforeUnmount } from 'vue';
const emits = defineEmits<{
    sizeChange: [Rectangle]
}>()

const chatViewRef = ref<HTMLDivElement | null>(null);
const resizeObserver = ref<ResizeObserver | null>(null);

const observeElement = () => {
    if (!chatViewRef.value) return;

    // 创建 ResizeObserver 实例
    resizeObserver.value = new ResizeObserver((entries) => {
        for (const entry of entries) {
            const bounds = getBounds();
            emits('sizeChange', bounds)
        }
    });

    // 开始观察
    resizeObserver.value.observe(chatViewRef.value);
};

const getBounds = () => {
    const rect = chatViewRef.value!.getBoundingClientRect();
    return {
        width: rect.width,
        height: rect.height,
        x: rect.x,
        y: rect.y,
    }
}

onMounted(() => {
    observeElement();
});

onBeforeUnmount(() => {
    // 组件卸载时停止观察
    if (resizeObserver.value && chatViewRef.value) {
        resizeObserver.value.unobserve(chatViewRef.value);
    }
});

defineExpose({
    getBounds
})

</script>