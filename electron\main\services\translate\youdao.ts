import axios from "axios";
import crypto from 'crypto';
import { ITranslationRoute } from "../../interface"
import { storeManager } from "../../modules/store";
import { TranslationType, VendorType } from "../../enums";

interface YoudaoTranslationResult {
    errorCode: string;
    query: string;
    translation: string[];
    basic?: {
        phonetic?: string;
        explains?: string[];
    };
    web?: {
        key: string;
        value: string[];
    }[];
}

class YoudaoTranslationService {
    /**
   * 获取所有可用的有道翻译线路
   */
    async getAvailableYoudaoRoutes(): Promise<ITranslationRoute[]> {
        const routes = await storeManager.getTranslateRoutes();
        return routes.filter(
            (route) =>
                route.vendor === VendorType.YOUDAO && route.type === TranslationType.TEXT && route.isActive,
        );
    }

    /**
     * 获取所有可用的有道图片翻译线路
     */
    async getAvailableYoudaoImageRoutes(): Promise<ITranslationRoute[]> {
        const routes = await storeManager.getTranslateRoutes();
        return routes.filter(
            (route) =>
                route.vendor === VendorType.YOUDAO &&
                route.type === TranslationType.IMAGE &&
                route.isActive,
        );
    }

    /**
   * 生成有道翻译签名
   */
    private generateSign(
        appKey: string,
        query: string,
        salt: string,
        curtime: string,
        appSecret: string,
    ): string {
        const input =
            query.length <= 20
                ? query
                : query.substring(0, 10) + query.length + query.substring(query.length - 10);
        const str = appKey + input + salt + curtime + appSecret;
        return crypto.createHash('sha256').update(str).digest('hex');
    }

    /**
     * 执行翻译
     */
    async translate(
        text: string,
        from: string,
        to: string,
        route: ITranslationRoute,
    ): Promise<string | null> {
        try {
            const salt = Date.now().toString();
            const curtime = Math.floor(Date.now() / 1000).toString();
            const sign = this.generateSign(route.apiKey, text, salt, curtime, route.apiSecret || '');

            const response = await axios.post<YoudaoTranslationResult>('https://openapi.youdao.com/api', {
                q: text,
                from,
                to,
                appKey: route.apiKey,
                salt,
                sign,
                signType: 'v3',
                curtime,
            });

            if (
                response.data.errorCode === '0' &&
                response.data.translation &&
                response.data.translation.length > 0
            ) {
                return response.data.translation[0];
            }

            return null;
        } catch (error) {
            return null;
        }
    }

    /**
     * 执行图片翻译
     */
    async translateImage(
        imageBase64: string,
        from: string,
        to: string,
        route: ITranslationRoute,
    ): Promise<string | null> {
        try {
            const salt = Date.now().toString();
            const curtime = Math.floor(Date.now() / 1000).toString();
            const sign = this.generateSign(
                route.apiKey,
                imageBase64,
                salt,
                curtime,
                route.apiSecret || '',
            );

            const response = await axios.post('https://openapi.youdao.com/ocrtransapi', {
                type: '1',
                q: imageBase64,
                from,
                to,
                appKey: route.apiKey,
                salt,
                sign,
                signType: 'v3',
                curtime,
                docType: 'json',
            });

            if (response.data.errorCode === '0' && response.data.resRegions) {
                // 合并所有区域的翻译结果
                return response.data.resRegions.map((region) => region.tranContent).join('\n');
            }

            return null;
        } catch (error) {
            return null;
        }
    }

    /**
     * 智能翻译（失败时自动切换线路）
     */
    async smartTranslate(
        text: string,
        from: string,
        to: string,
    ): Promise<string | null> {
        const routes = await this.getAvailableYoudaoRoutes();

        if (routes.length === 0) {
            return '没有可用的翻译线路';
        }

        // 尝试每条线路，直到成功或全部失败
        for (const route of routes) {
            const result = await this.translate(text, from, to, route);
            if (result !== null) {
                return result;
            }
        }

        return '没有可用的翻译线路';
    }

    /**
     * 智能图片翻译（失败时自动切换线路）
     */
    async smartTranslateImage(
        imageBase64: string,
        from: string,
        to: string,
    ): Promise<[string | undefined, string | undefined]> {
        const routes = await this.getAvailableYoudaoImageRoutes();

        if (routes.length === 0) {
            return [, '没有可用的翻译线路'];
        }

        // 尝试每条线路，直到成功或全部失败
        for (const route of routes) {
            const result = await this.translateImage(imageBase64, from, to, route);
            if (result !== null) {
                return [result, undefined];
            }
        }
        return [, '没有可用的翻译线路'];
    }
}

export default new YoudaoTranslationService()