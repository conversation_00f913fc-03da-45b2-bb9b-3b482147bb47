import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import { PointsProduct } from '../entities/PointsProduct';

export interface CreatePointsProductOptions {
  name: string;
  description?: string;
  points: number;
  price: number;
  discountRate?: number;
  bonusPoints?: number;
  isActive?: boolean;
  sort?: number;
}

export interface UpdatePointsProductOptions {
  name?: string;
  description?: string;
  points?: number;
  price?: number;
  discountRate?: number;
  bonusPoints?: number;
  isActive?: boolean;
  sort?: number;
}

/**
 * 积分商品服务
 */
export class PointsProductService {
  private repository: Repository<PointsProduct>;

  constructor() {
    this.repository = AppDataSource.getRepository(PointsProduct);
  }

  /**
   * 创建积分商品
   */
  async create(options: CreatePointsProductOptions): Promise<PointsProduct> {
    const product = this.repository.create({
      ...options,
      discountRate: options.discountRate ?? 1.0,
      bonusPoints: options.bonusPoints ?? 0,
      isActive: options.isActive ?? true,
      sort: options.sort ?? 0,
    });

    return await this.repository.save(product);
  }

  /**
   * 更新积分商品
   */
  async update(id: string, options: UpdatePointsProductOptions): Promise<PointsProduct> {
    const product = await this.findById(id);
    if (!product) {
      throw new Error('积分商品不存在');
    }

    Object.assign(product, options);
    return await this.repository.save(product);
  }

  /**
   * 根据ID查找积分商品
   */
  async findById(id: string): Promise<PointsProduct | null> {
    return await this.repository.findOne({ where: { id } });
  }

  /**
   * 获取所有启用的积分商品
   */
  async findActiveProducts(): Promise<PointsProduct[]> {
    return await this.repository.find({
      where: { isActive: true },
      order: { sort: 'ASC', createdAt: 'ASC' },
    });
  }

  /**
   * 获取所有积分商品（包括禁用的）
   */
  async findAll(): Promise<PointsProduct[]> {
    return await this.repository.find({
      order: { sort: 'ASC', createdAt: 'ASC' },
    });
  }

  /**
   * 删除积分商品
   */
  async delete(id: string): Promise<void> {
    const result = await this.repository.delete(id);
    if (result.affected === 0) {
      throw new Error('积分商品不存在');
    }
  }

  /**
   * 启用/禁用积分商品
   */
  async toggleActive(id: string): Promise<PointsProduct> {
    const product = await this.findById(id);
    if (!product) {
      throw new Error('积分商品不存在');
    }

    product.isActive = !product.isActive;
    return await this.repository.save(product);
  }

  /**
   * 批量创建积分商品
   */
  async batchCreate(products: CreatePointsProductOptions[]): Promise<PointsProduct[]> {
    const entities = products.map((options) =>
      this.repository.create({
        ...options,
        discountRate: options.discountRate ?? 1.0,
        bonusPoints: options.bonusPoints ?? 0,
        isActive: options.isActive ?? true,
        sort: options.sort ?? 0,
      }),
    );

    return await this.repository.save(entities);
  }
}
