<template>
    <div class="w-fit h-full pl-1 overflow-y-auto scrollbar-hide">
        <div class="bg-white rounded-lg w-[300px] h-full p-3 flex flex-col relative">
            <div class="font-bold flex-shrink-0">指纹浏览器</div>
            <div class="pt-4 flex-grow h-0 overflow-auto scrollbar-hide pb-16">
                <div>
                    <el-form ref="formRef" :model="form" :rules="rules" label-width="auto" label-position="top">
                        <el-form-item label="启用指纹浏览器" label-position="left">
                            <div class="w-full flex justify-end">
                                <el-switch v-model="form.enabled" />
                            </div>
                        </el-form-item>
                        <template v-if="form.enabled">
                            <el-form-item label="操作系统" label-position="top" prop="os">
                                <el-select v-model="form.os" placeholder="请选择操作系统">
                                    <el-option v-for="item in [
                                        { label: 'Windows 10', value: 'win10' },
                                        { label: 'Windows 11', value: 'win11' },
                                        { label: 'MacOS', value: 'macos' },
                                        { label: 'Linux', value: 'linux' }
                                    ]" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="浏览器" label-position="top" prop="browser">
                                <el-select v-model="form.browser" placeholder="请选择浏览器">
                                    <el-option v-for="item in [
                                        { label: 'Chrome', value: 'chrome' },
                                        { label: 'Firefox', value: 'firefox' },
                                        { label: 'Edge', value: 'edge' },
                                        { label: 'Safari', value: 'safari' }
                                    ]" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="屏幕分辨率" label-position="top" prop="resolution">
                                <el-select v-model="form.resolution" placeholder="请选择分辨率">
                                    <el-option v-for="item in [
                                        { label: '1920x1080', value: '1920x1080' },
                                        { label: '2560x1440', value: '2560x1440' },
                                        { label: '3840x2160', value: '3840x2160' }
                                    ]" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="语言" label-position="top" prop="language">
                                <el-select v-model="form.language" placeholder="请选择语言">
                                    <el-option v-for="item in [
                                        { label: '简体中文', value: 'zh-CN' },
                                        { label: '繁体中文', value: 'zh-TW' },
                                        { label: '英语', value: 'en-US' },
                                        { label: '日语', value: 'ja-JP' }
                                    ]" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="时区" label-position="top" prop="timezone">
                                <el-select v-model="form.timezone" placeholder="请选择时区">
                                    <el-option v-for="item in [
                                        { label: '(GMT+08:00) 北京', value: 'Asia/Shanghai' },
                                        { label: '(GMT+09:00) 东京', value: 'Asia/Tokyo' },
                                        { label: '(GMT+00:00) 伦敦', value: 'Europe/London' },
                                        { label: '(GMT-08:00) 洛杉矶', value: 'America/Los_Angeles' }
                                    ]" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="User-Agent" label-position="top" prop="userAgent">
                                <el-input v-model="form.userAgent" type="textarea" :rows="4"
                                    placeholder="请输入或随机生成 User-Agent" />
                                <div class="flex items-center justify-end mt-2">
                                    <el-button size="small" @click="generateRandomUserAgent">
                                        随机生成
                                    </el-button>
                                </div>
                            </el-form-item>
                            <el-form-item label="Cookie" label-position="top" prop="cookie">
                                <el-input v-model="form.cookie" type="textarea" :rows="3"
                                    placeholder="请输入 Cookie，每行一个" />
                            </el-form-item>
                            <el-form-item label="WebRTC" label-position="left">
                                <div class="w-full flex justify-end">
                                    <el-switch v-model="form.webrtc" />
                                </div>
                            </el-form-item>
                            <el-form-item label="Canvas" label-position="left">
                                <div class="w-full flex justify-end">
                                    <el-switch v-model="form.canvas" />
                                </div>
                            </el-form-item>
                            <el-form-item label="WebGL" label-position="left">
                                <div class="w-full flex justify-end">
                                    <el-switch v-model="form.webgl" />
                                </div>
                            </el-form-item>
                        </template>
                    </el-form>
                </div>
            </div>
            <div class="absolute bottom-0 left-0 right-0 p-3 bg-white border-t">
                <div class="flex justify-end gap-2 w-full">
                    <el-button class="flex-grow" @click="resetForm">重置</el-button>
                    <el-button class="flex-grow" type="primary" @click="saveConfig">保存</el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { $message } from '@/utils/message'
import type { FormInstance, FormRules } from 'element-plus'

interface FingerprintConfig {
    enabled: boolean
    os: string
    browser: string
    resolution: string
    language: string
    timezone: string
    userAgent: string
    cookie: string
    webrtc: boolean
    canvas: boolean
    webgl: boolean
}

const props = defineProps<{
    chatId: string
}>()

const formRef = ref<FormInstance>()

// 表单校验规则
const rules = ref<FormRules>({
    os: [
        { required: true, message: '请选择操作系统', trigger: 'change' }
    ],
    browser: [
        { required: true, message: '请选择浏览器', trigger: 'change' }
    ],
    resolution: [
        { required: true, message: '请选择分辨率', trigger: 'change' }
    ],
    language: [
        { required: true, message: '请选择语言', trigger: 'change' }
    ],
    timezone: [
        { required: true, message: '请选择时区', trigger: 'change' }
    ],
    userAgent: [
        { required: true, message: '请输入或随机生成 User-Agent', trigger: 'blur' }
    ]
})

// 从 Electron 读取配置
const loadConfig = async (): Promise<FingerprintConfig> => {
    try {
        const config = await window.ipcRenderer.invoke('get-fingerprint-config', props.chatId)
        if (config) {
            return config as FingerprintConfig
        }
    } catch (error) {
        console.error('从 Electron 获取配置失败:', error)
    }
    return {
        enabled: false,
        os: '',
        browser: '',
        resolution: '',
        language: '',
        timezone: '',
        userAgent: '',
        cookie: '',
        webrtc: false,
        canvas: false,
        webgl: false
    }
}

const defaultConfig: FingerprintConfig = {
    enabled: false,
    os: '',
    browser: '',
    resolution: '',
    language: '',
    timezone: '',
    userAgent: '',
    cookie: '',
    webrtc: false,
    canvas: false,
    webgl: false
}

const form = ref<FingerprintConfig>(defaultConfig)

// 保存配置
const saveConfig = async () => {
    if (!form.value.enabled) {
        try {
            // 转换为纯对象
            const config = JSON.parse(JSON.stringify(form.value))
            await window.ipcRenderer.invoke('set-fingerprint-config', props.chatId, config)
            $message.success('保存成功')
        } catch (error) {
            console.error('保存配置到 Electron 失败:', error)
            $message.error('保存配置失败')
        }
        return
    }

    if (!formRef.value) return

    try {
        await formRef.value.validate(async (valid, fields) => {
            if (valid) {
                // 转换为纯对象
                const config = JSON.parse(JSON.stringify(form.value))
                await window.ipcRenderer.invoke('set-fingerprint-config', props.chatId, config)
                $message.success('保存成功')
            } else {
                console.log('校验失败', fields)
                throw new Error('表单校验失败')
            }
        })
    } catch (error) {
        $message.error('请完善表单信息')
    }
}

// 重置表单
const resetForm = async () => {
    form.value = await loadConfig()
    formRef.value?.clearValidate()
    $message.success('已重置')
}

// 监听聊天ID变化，重新加载配置
watch(() => props.chatId, async () => {
    form.value = await loadConfig()
    formRef.value?.clearValidate()
})

// 监听启用状态变化，清除校验信息
watch(() => form.value.enabled, (newVal) => {
    if (!newVal) {
        formRef.value?.clearValidate()
    }
})

onMounted(async () => {
    form.value = await loadConfig()
})

// 生成随机 User-Agent
const generateRandomUserAgent = () => {
    const osOptions = [
        { value: 'win10', info: 'Windows NT 10.0' },
        { value: 'win11', info: 'Windows NT 11.0' },
        { value: 'macos', info: 'Macintosh; Intel Mac OS X 10_15_7' },
        { value: 'linux', info: 'X11; Linux x86_64' }
    ]

    const browserOptions = [
        {
            name: 'chrome',
            versions: ['120.0.0.0', '119.0.0.0', '118.0.0.0', '117.0.0.0'],
            template: (os: string, version: string) =>
                `Mozilla/5.0 (${os}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${version} Safari/537.36`
        },
        {
            name: 'firefox',
            versions: ['121.0', '120.0', '119.0', '118.0'],
            template: (os: string, version: string) =>
                `Mozilla/5.0 (${os}; rv:${version}) Gecko/20100101 Firefox/${version}`
        },
        {
            name: 'edge',
            versions: ['120.0.0.0', '119.0.0.0', '118.0.0.0', '117.0.0.0'],
            template: (os: string, version: string) =>
                `Mozilla/5.0 (${os}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${version} Safari/537.36 Edg/${version}`
        }
    ]

    // 随机选择操作系统
    const randomOs = osOptions[Math.floor(Math.random() * osOptions.length)]

    // 随机选择浏览器（如果是 macOS，可能会包含 Safari）
    let availableBrowsers = [...browserOptions]
    if (randomOs.value === 'macos') {
        availableBrowsers.push({
            name: 'safari',
            versions: ['17.2', '17.1', '17.0', '16.6'],
            template: (os: string, version: string) =>
                `Mozilla/5.0 (${os}) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/${version} Safari/605.1.15`
        })
    }

    const randomBrowser = availableBrowsers[Math.floor(Math.random() * availableBrowsers.length)]
    const randomVersion = randomBrowser.versions[Math.floor(Math.random() * randomBrowser.versions.length)]

    // 生成 User-Agent
    form.value.userAgent = randomBrowser.template(randomOs.info, randomVersion)
}
</script>

<style>
/* 自定义滚动条隐藏 */
.scrollbar-hide {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari and Opera */
}
</style>