import { DEV_SERVER_URL, INDEX_HTML } from "../config/config";
import {
    launchWindowOptions,
    loginWindowOptions,
    mainWindowOptions,
    updateWindowOptions,
    backgroundWindowOptions
} from './options/index'
import { BaseWindowsManager } from "./baseWindow";
import chatWindowManager from "./chatWindow";
import { Rectangle } from "electron";
class CoreWindowsManager extends BaseWindowsManager {

    setBcakgroundWindowBounds(bounds: Rectangle) {
        const bgWindow = this.getWindow('background');
        bgWindow?.setBounds(bounds, false);
    }

    createBcakgroundWindow(options?: Electron.BrowserWindowConstructorOptions) {
        const winId = 'background';
        const url = DEV_SERVER_URL ? `${DEV_SERVER_URL}/#/background` : `${INDEX_HTML}/#/background`;
        const win = this.createWindow(winId, url, { ...backgroundWindowOptions, ...options });
        return win;
    }

    createMainWindow() {
        let bgWindow = this.getWindow('background');
        if (!bgWindow) {
            bgWindow = this.createBcakgroundWindow({
                minWidth: mainWindowOptions.minWidth,
                minHeight: mainWindowOptions.minHeight,
                width: mainWindowOptions.width,
                height: mainWindowOptions.height,
            });
        }
        const winId = 'main';
        const url = DEV_SERVER_URL ? DEV_SERVER_URL : INDEX_HTML;
        const win = this.createWindow(winId, url, {
            ...mainWindowOptions,
            parent: bgWindow,
        })
        if (import.meta.env.DEV) {
            win.webContents.openDevTools({ mode: 'detach' })
        }
        win.on('ready-to-show', () => {
            this.setBcakgroundWindowBounds(win.getBounds());
        })
        win.on('move', () => {
            chatWindowManager.setBounds();
            this.setBcakgroundWindowBounds(win.getBounds());
        })
        win.on('resize', () => {
            chatWindowManager.setBounds();
            this.setBcakgroundWindowBounds(win.getBounds());
        })
        win.on('closed', () => {
            bgWindow?.close();
        })
        win.on('minimize', () => {
            bgWindow?.minimize();
        })
        win.on('maximize', () => {
            bgWindow?.unmaximize();
        })
        win.on('unmaximize', () => {
            bgWindow?.unmaximize();
        })
        win.on('show', () => {
            bgWindow?.show();
            this.setBcakgroundWindowBounds(win.getBounds());
        })
        return win;
    }

    createLoginWindow() {
        let bgWindow = this.getWindow('background');
        if (!bgWindow) {
            bgWindow = this.createBcakgroundWindow({
                width: loginWindowOptions.width,
                height: loginWindowOptions.height,
            });
        }
        const winId = 'login';
        const url = DEV_SERVER_URL ? `${DEV_SERVER_URL}/#/login` : `${INDEX_HTML}/#/login`;
        const win = this.createWindow(winId, url, { ...loginWindowOptions, parent: bgWindow });
        win.on('ready-to-show', () => {
            this.setBcakgroundWindowBounds(win.getBounds());
        })
        win.on('move', () => {
            this.setBcakgroundWindowBounds(win.getBounds());
        })
        win.on('resize', () => {
            this.setBcakgroundWindowBounds(win.getBounds());
        })
        win.on('closed', () => {
            bgWindow?.close();
        })
        win.on('minimize', () => {
            bgWindow?.minimize();
        })
        win.on('maximize', () => {
            bgWindow?.unmaximize();
        })
        win.on('unmaximize', () => {
            bgWindow?.unmaximize();
        })
        win.on('show', () => {
            bgWindow?.show();
            this.setBcakgroundWindowBounds(win.getBounds());
        })
        return win;
    }

    createLaunchWindow() {
        const winId = 'launch';
        const url = DEV_SERVER_URL ? `${DEV_SERVER_URL}/#/launch` : `${INDEX_HTML}/#/launch`;
        const win = this.createWindow(winId, url, launchWindowOptions);
        return win;
    }

    createUpdateWindow() {
        const winId = 'update';
        const url = DEV_SERVER_URL ? `${DEV_SERVER_URL}/#/update` : `${INDEX_HTML}/#/update`;
        const win = this.createWindow(winId, url, updateWindowOptions);
        return win;
    }

}

export default new CoreWindowsManager();