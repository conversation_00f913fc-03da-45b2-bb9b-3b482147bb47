# 管理端会员管理模块 API 文档

## 概述

管理端会员管理模块提供会员商品管理和权限管理功能，包括商品的增删改查、权限的配置管理等。

**基础路径:** `/api/admin`

## 会员商品管理

### 1. 获取会员商品列表

获取会员商品的分页列表。

- **URL:** `/api/admin/membership/products`
- **方法:** `GET`
- **权限:** 需要管理员认证
- **查询参数:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| page | number | 否 | 页码，默认为1 |
| pageSize | number | 否 | 每页条数，默认为10 |

- **成功响应:**

```json
{
  "code": 0,
  "message": "获取会员商品列表成功",
  "data": {
    "items": [
      {
        "id": "product_001",
        "name": "基础会员",
        "description": "基础会员套餐",
        "price": 99.00,
        "duration": 30,
        "sort": 1,
        "isActive": true,
        "iconUrl": "https://example.com/icon.png",
        "createdAt": "2023-01-01T00:00:00.000Z",
        "updatedAt": "2023-01-01T00:00:00.000Z"
      }
    ],
    "total": 10,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

### 2. 获取会员商品详情

获取指定会员商品的详细信息。

- **URL:** `/api/admin/membership/products/{id}`
- **方法:** `GET`
- **权限:** 需要管理员认证
- **路径参数:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | string | 是 | 会员商品ID |

- **成功响应:**

```json
{
  "code": 0,
  "message": "获取会员商品详情成功",
  "data": {
    "id": "product_001",
    "name": "基础会员",
    "description": "基础会员套餐",
    "price": 99.00,
    "duration": 30,
    "sort": 1,
    "isActive": true,
    "iconUrl": "https://example.com/icon.png",
    "permissions": [
      {
        "id": "perm_001",
        "name": "基础翻译",
        "key": "basic.translation"
      }
    ],
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### 3. 创建会员商品

创建新的会员商品。

- **URL:** `/api/admin/membership/products`
- **方法:** `POST`
- **权限:** 需要管理员认证
- **请求体:**

```json
{
  "name": "高级会员",
  "description": "高级会员套餐，包含更多功能",
  "price": 199.00,
  "duration": 30,
  "sort": 2,
  "isActive": true,
  "iconUrl": "https://example.com/premium-icon.png",
  "permissionIds": ["perm_001", "perm_002"]
}
```

**请求参数说明:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| name | string | 是 | 商品名称 |
| description | string | 否 | 商品描述 |
| price | number | 是 | 商品价格 |
| duration | number | 是 | 有效期（天数） |
| sort | number | 否 | 排序值 |
| isActive | boolean | 否 | 是否启用 |
| iconUrl | string | 否 | 图标URL |
| permissionIds | string[] | 否 | 权限ID列表 |

- **成功响应:**

```json
{
  "code": 0,
  "message": "创建会员商品成功",
  "data": {
    "id": "product_002",
    "name": "高级会员",
    "description": "高级会员套餐，包含更多功能",
    "price": 199.00,
    "duration": 30,
    "sort": 2,
    "isActive": true,
    "iconUrl": "https://example.com/premium-icon.png",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### 4. 更新会员商品

更新指定的会员商品信息。

- **URL:** `/api/admin/membership/products/{id}`
- **方法:** `PUT`
- **权限:** 需要管理员认证
- **路径参数:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | string | 是 | 会员商品ID |

- **请求体:** 同创建接口，所有字段均为可选

- **成功响应:**

```json
{
  "code": 0,
  "message": "更新会员商品成功",
  "data": {
    "id": "product_002",
    "name": "高级会员（更新）",
    "description": "高级会员套餐，包含更多功能",
    "price": 299.00,
    "duration": 30,
    "sort": 2,
    "isActive": true,
    "iconUrl": "https://example.com/premium-icon.png",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-02T00:00:00.000Z"
  }
}
```

### 5. 删除会员商品

删除指定的会员商品。

- **URL:** `/api/admin/membership/products/{id}`
- **方法:** `DELETE`
- **权限:** 需要管理员认证
- **路径参数:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | string | 是 | 会员商品ID |

- **成功响应:**

```json
{
  "code": 0,
  "message": "删除会员商品成功"
}
```

## 权限管理

### 6. 获取权限列表

获取权限的分页列表。

- **URL:** `/api/admin/permissions`
- **方法:** `GET`
- **权限:** 需要管理员认证
- **查询参数:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| page | number | 否 | 页码，默认为1 |
| pageSize | number | 否 | 每页条数，默认为10 |

- **成功响应:**

```json
{
  "code": 0,
  "message": "获取权限列表成功",
  "data": {
    "items": [
      {
        "id": "perm_001",
        "name": "基础翻译",
        "key": "basic.translation",
        "description": "基础翻译功能权限",
        "group": "翻译功能",
        "isActive": true,
        "createdAt": "2023-01-01T00:00:00.000Z",
        "updatedAt": "2023-01-01T00:00:00.000Z"
      }
    ],
    "total": 5,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

### 7. 获取权限分组列表

获取按分组组织的权限列表。

- **URL:** `/api/admin/permissions/group`
- **方法:** `GET`
- **权限:** 需要管理员认证

- **成功响应:**

```json
{
  "code": 0,
  "message": "获取权限列表成功",
  "data": {
    "翻译功能": [
      {
        "id": "perm_001",
        "name": "基础翻译",
        "key": "basic.translation",
        "description": "基础翻译功能权限",
        "group": "翻译功能",
        "isActive": true
      }
    ],
    "会员功能": [
      {
        "id": "perm_002",
        "name": "高级翻译",
        "key": "premium.translation",
        "description": "高级翻译功能权限",
        "group": "会员功能",
        "isActive": true
      }
    ]
  }
}
```

### 8. 获取权限详情

获取指定权限的详细信息。

- **URL:** `/api/admin/permissions/{id}`
- **方法:** `GET`
- **权限:** 需要管理员认证
- **路径参数:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | string | 是 | 权限ID |

- **成功响应:**

```json
{
  "code": 0,
  "message": "获取权限详情成功",
  "data": {
    "id": "perm_001",
    "name": "基础翻译",
    "key": "basic.translation",
    "description": "基础翻译功能权限",
    "group": "翻译功能",
    "isActive": true,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### 9. 创建权限

创建新的权限。

- **URL:** `/api/admin/permissions`
- **方法:** `POST`
- **权限:** 需要管理员认证
- **请求体:**

```json
{
  "name": "图片翻译",
  "key": "image.translation",
  "description": "图片翻译功能权限",
  "group": "翻译功能",
  "isActive": true
}
```

**请求参数说明:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| name | string | 是 | 权限名称 |
| key | string | 是 | 权限标识（唯一） |
| description | string | 否 | 权限描述 |
| group | string | 否 | 权限分组 |
| isActive | boolean | 否 | 是否启用 |

- **成功响应:**

```json
{
  "code": 0,
  "message": "创建权限成功",
  "data": {
    "id": "perm_003",
    "name": "图片翻译",
    "key": "image.translation",
    "description": "图片翻译功能权限",
    "group": "翻译功能",
    "isActive": true,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### 10. 更新权限

更新指定的权限信息。

- **URL:** `/api/admin/permissions/{id}`
- **方法:** `PUT`
- **权限:** 需要管理员认证
- **路径参数:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | string | 是 | 权限ID |

- **请求体:** 同创建接口，所有字段均为可选

- **成功响应:**

```json
{
  "code": 0,
  "message": "更新权限成功",
  "data": {
    "id": "perm_003",
    "name": "图片翻译（更新）",
    "key": "image.translation",
    "description": "图片翻译功能权限",
    "group": "翻译功能",
    "isActive": true,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-02T00:00:00.000Z"
  }
}
```

### 11. 删除权限

删除指定的权限。

- **URL:** `/api/admin/permissions/{id}`
- **方法:** `DELETE`
- **权限:** 需要管理员认证
- **路径参数:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | string | 是 | 权限ID |

- **成功响应:**

```json
{
  "code": 0,
  "message": "删除权限成功"
}
```

## 错误码说明

| 错误码 | 描述 |
|--------|------|
| 400 | 请求参数错误或验证失败 |
| 401 | 未授权或认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有接口都需要管理员身份认证
2. 会员商品的价格单位为元，支持小数
3. 权限标识（key）必须唯一，建议使用点分格式
4. 删除会员商品或权限前请确认没有关联数据
5. 权限分组用于前端展示，便于管理
