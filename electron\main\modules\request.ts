import axios from 'axios'
import { storeManager } from './store'
const request = axios.create({
    baseURL: "http://127.0.0.1:3000/api",
    timeout: 1000,
});

request.interceptors.request.use(async (config) => {
    if (await storeManager.getToken()) {
        config.headers['Authorization'] = `Bearer ${await storeManager.getToken()}`
    }
    return config;
}, error => {
    return Promise.reject(error);
})

request.interceptors.response.use(response => {
    const res = response.data
    // code不等于100则抛出异常信息
    if (res.code !== 200) {
        console.log(res);
        return Promise.reject(new Error(res.message || 'Error'))
    } else {
        return res
    }
}, error => {
    console.log(error);
    return Promise.reject(error);
})

export default request;