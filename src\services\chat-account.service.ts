import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import { ChatAccount } from '../entities/ChatAccount';
import { Client } from '../entities/Client';

export class ChatAccountService {
  private chatAccountRepository: Repository<ChatAccount>;
  private clientRepository: Repository<Client>;

  constructor() {
    this.chatAccountRepository = AppDataSource.getRepository(ChatAccount);
    this.clientRepository = AppDataSource.getRepository(Client);
  }

  // 创建聊天账号
  async create(data: Partial<ChatAccount>, clientId: string): Promise<ChatAccount> {
    const client = await this.clientRepository.findOneOrFail({ where: { id: clientId } });
    const chatAccount = this.chatAccountRepository.create({
      ...data,
      client,
    });
    return await this.chatAccountRepository.save(chatAccount);
  }

  // 更新聊天账号
  async update(id: string, data: Partial<ChatAccount>): Promise<ChatAccount> {
    await this.chatAccountRepository.update(id, data);
    return await this.chatAccountRepository.findOneOrFail({
      where: { id },
      relations: ['client'],
    });
  }

  // 删除聊天账号
  async delete(id: string): Promise<void> {
    await this.chatAccountRepository.delete(id);
  }

  // 获取单个聊天账号
  async findOne(id: string): Promise<ChatAccount> {
    return await this.chatAccountRepository.findOneOrFail({
      where: { id },
      relations: ['client'],
    });
  }

  // 获取聊天账号列表
  async findAll(clientId: string): Promise<ChatAccount[]> {
    return await this.chatAccountRepository.find({
      where: { client: { id: clientId } },
      relations: ['client'],
    });
  }
}
