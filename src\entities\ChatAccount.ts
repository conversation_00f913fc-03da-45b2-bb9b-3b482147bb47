import { Entity, Column, Index, ManyToOne, <PERSON>in<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { Client } from './Client';
import { BaseEntity } from './BaseEntity';

/**
 * 第三方平台聊天账号实体
 */
@Entity('chat_account')
export class ChatAccount extends BaseEntity {
  @Column({
    nullable: true,
    comment: '账号唯一ID',
  })
  @Index()
  accountId?: string;

  @ManyToOne(() => Client)
  @JoinColumn({ name: 'client_id' })
  client!: Client;

  @Column({
    length: 100,
    nullable: true,
    comment: '昵称',
  })
  nickname?: string;

  @Column({
    nullable: true,
    comment: '头像',
  })
  avatar?: string;

  @Column({
    nullable: true,
    comment: '平台',
  })
  platform?: string;
}
