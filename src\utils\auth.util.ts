import bcrypt from 'bcryptjs';
import jwt, { Secret } from 'jsonwebtoken';
import { JwtPayload } from '../types/auth.interface';
import { JWT_SECRET } from '../config/jwt';

/**
 * 哈希密码
 */
export const hashPassword = async (password: string): Promise<string> => {
  const salt = await bcrypt.genSalt(10);
  return bcrypt.hash(password, salt);
};

/**
 * 比较密码是否匹配
 */
export const comparePassword = async (
  password: string,
  hashedPassword: string,
): Promise<boolean> => {
  return bcrypt.compare(password, hashedPassword);
};

/**
 * 生成JWT令牌
 */
export const generateToken = (user: any): string => {
  const secret = (JWT_SECRET || 'default_secret_key') as Secret;
  return jwt.sign(
    {
      id: user.id,
      email: user.email,
      type: 'client',
      tenantId: user.tenantId,
    },
    secret,
    { expiresIn: '24h' },
  );
};

/**
 * 验证JWT令牌
 */
export const verifyToken = (token: string): JwtPayload => {
  const secret = (JWT_SECRET || 'your_jwt_secret') as Secret;
  return jwt.verify(token, secret) as JwtPayload;
};
