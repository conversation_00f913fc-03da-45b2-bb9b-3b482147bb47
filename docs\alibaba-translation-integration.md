# 阿里云图片翻译集成说明

## 概述

本文档说明如何在 SCRM 系统中使用阿里云机器翻译服务进行图片翻译。

## 功能特性

- ✅ 支持阿里云机器翻译 API
- ✅ 图片翻译功能
- ✅ 多语言支持（17种语言）
- ✅ 智能路由切换
- ✅ 前端管理界面

## 支持的语言

阿里云翻译支持以下语言：

| 语言代码 | 语言名称 |
|----------|----------|
| zh | 中文 |
| en | 英语 |
| ja | 日语 |
| ko | 韩语 |
| fr | 法语 |
| es | 西班牙语 |
| it | 意大利语 |
| de | 德语 |
| tr | 土耳其语 |
| ru | 俄语 |
| pt | 葡萄牙语 |
| vi | 越南语 |
| id | 印尼语 |
| th | 泰语 |
| ms | 马来语 |
| ar | 阿拉伯语 |
| hi | 印地语 |

## 配置步骤

### 1. 获取阿里云 API 密钥

1. 登录 [阿里云控制台](https://ecs.console.aliyun.com/)
2. 进入 **访问控制 RAM** > **用户管理**
3. 创建新用户或使用现有用户
4. 为用户添加 **AliyunMTFullAccess** 权限
5. 创建 AccessKey，获得 **AccessKey ID** 和 **AccessKey Secret**

### 2. 在系统中配置翻译路由

#### 通过前端界面配置：

1. 登录系统，进入 **翻译路由管理**
2. 点击 **创建翻译路由**
3. 选择翻译类型：**图片翻译**
4. 选择供应商：**阿里云翻译**
5. 填写配置信息：
   - **API Key**: 阿里云 AccessKey ID
   - **API Secret**: 阿里云 AccessKey Secret
   - **是否启用**: 开启
   - **排序值**: 设置优先级（数值越小优先级越高）

#### 通过 API 配置：

```bash
POST /api/client/translation-routes/create
Authorization: Bearer {token}
Content-Type: application/json

{
  "type": "image",
  "vendor": "alibaba",
  "apiKey": "your_access_key_id",
  "apiSecret": "your_access_key_secret",
  "isActive": true,
  "sort": 1,
  "accountId": "your_account_id"
}
```

## 使用方法

### 图片翻译 API

```bash
POST /api/client/translation/translate-image
Authorization: Bearer {token}
Content-Type: application/json

{
  "imageBase64": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "from": "en",
  "to": "zh",
  "vendor": "alibaba"
}
```

**响应示例：**

```json
{
  "code": 200,
  "message": "图片翻译成功",
  "data": {
    "text": "翻译后的文本内容"
  }
}
```

### 前端调用示例

```javascript
import { translateImageAPI } from '@/api/translation'

// 图片翻译
const translateImage = async (imageFile, fromLang, toLang) => {
  try {
    // 将图片转换为 base64
    const imageBase64 = await fileToBase64(imageFile)
    
    const response = await translateImageAPI({
      imageBase64,
      from: fromLang,
      to: toLang,
      vendor: 'alibaba'
    })
    
    console.log('翻译结果:', response.data.text)
    return response.data.text
  } catch (error) {
    console.error('翻译失败:', error)
    throw error
  }
}

// 文件转 base64 工具函数
const fileToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result)
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}
```

## 错误处理

### 常见错误码

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式 |
| 401 | 认证失败 | 检查 AccessKey 配置 |
| 403 | 权限不足 | 确认 RAM 用户有 MT 权限 |
| 500 | 服务器错误 | 检查网络连接和服务状态 |

### 调试建议

1. **检查 API 密钥**: 确保 AccessKey ID 和 Secret 正确
2. **验证权限**: 确认 RAM 用户有机器翻译权限
3. **检查网络**: 确保服务器能访问阿里云 API
4. **查看日志**: 检查服务端日志获取详细错误信息

## 性能优化

### 图片格式建议

- **支持格式**: PNG, JPG, GIF, BMP
- **文件大小**: 建议小于 5MB
- **分辨率**: 建议不超过 4096x4096

### 并发控制

- 阿里云 API 有并发限制，建议控制并发请求数
- 可配置多个翻译路由实现负载均衡
- 实现请求队列避免超出限制

## 费用说明

阿里云机器翻译按字符数收费：

- **图片翻译**: 按识别的字符数计费
- **免费额度**: 新用户有一定免费额度
- **计费详情**: 请参考 [阿里云机器翻译定价](https://www.aliyun.com/price/product#/mt/detail)

## 技术实现

### 核心文件

1. **服务类**: `src/services/alibaba-translation.service.ts`
2. **控制器**: `src/controllers/client/translation.controller.ts`
3. **路由服务**: `src/services/translation-route.service.ts`
4. **实体定义**: `src/entities/TranslationRoute.ts`

### 依赖包

```json
{
  "@alicloud/alimt20181012": "^2.0.0",
  "@alicloud/openapi-client": "^0.4.0",
  "@alicloud/tea-util": "^1.4.0"
}
```

## 总结

阿里云图片翻译已成功集成到 SCRM 系统中，提供了完整的图片翻译解决方案。用户可以通过前端界面或 API 调用来使用这项功能，系统支持智能路由切换和多语言翻译。
