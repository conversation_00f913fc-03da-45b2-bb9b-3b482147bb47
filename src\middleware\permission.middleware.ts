import { Context, Next } from 'koa';
import { ClientMembershipService } from '../services/client-membership.service';

/**
 * 创建权限检查中间件
 * @param permissionKey 权限标识
 * @returns 中间件函数
 */
export function requirePermission(permissionKey: string) {
  const clientMembershipService = new ClientMembershipService();

  return async (ctx: Context, next: Next) => {
    // 确保用户已登录
    if (!ctx.state.user) {
      ctx.status = 401;
      ctx.body = {
        code: 401,
        message: '请先登录',
      };
      return;
    }

    // 检查用户是否有该权限
    const hasPermission = await clientMembershipService.checkClientPermission(
      ctx.state.user.id,
      permissionKey,
    );

    if (!hasPermission) {
      ctx.status = 403;
      ctx.body = {
        code: 403,
        message: '您没有权限访问该功能，请开通会员',
      };
      return;
    }

    await next();
  };
}

/**
 * 简单检查用户是否为会员，不需要具体权限
 * @returns 中间件函数
 */
export function requireMembership() {
  const clientMembershipService = new ClientMembershipService();

  return async (ctx: Context, next: Next) => {
    // 确保用户已登录
    if (!ctx.state.user) {
      ctx.status = 401;
      ctx.body = {
        code: 401,
        message: '请先登录',
      };
      return;
    }

    // 检查用户是否有有效会员
    const hasMembership = await clientMembershipService.checkAndUpdateMembershipStatus(
      ctx.state.user.id,
    );

    if (!hasMembership) {
      ctx.status = 403;
      ctx.body = {
        code: 403,
        message: '您没有有效会员，请开通会员',
      };
      return;
    }

    await next();
  };
}
