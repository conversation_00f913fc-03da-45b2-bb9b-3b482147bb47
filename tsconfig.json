{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "noEmit": true, "types": ["element-plus/global"], "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src", "global.d.ts"], "references": [{"path": "./tsconfig.node.json"}]}