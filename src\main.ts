import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import App from './App.vue'
import 'element-plus/dist/index.css'
import 'virtual:svg-icons-register'
// 通用字体
import 'vfonts/Lato.css'
// 等宽字体
import 'vfonts/FiraCode.css'
import "./style.css"
import "./element.scss"

import { setupRouter } from './router';
import { setupStores } from './stores'
import { setupElectronListener } from './electronListener'

import './permission'

function bootstrap() {
  const app = createApp(App);

  setupRouter(app)
  setupStores(app)
  setupElectronListener()
  app.use(ElementPlus)
  app.mount('#app').$nextTick(() => {
    postMessage({ payload: 'removeLoading' }, '*')
  })

}

bootstrap()