<template>
    <el-dialog v-model="visible" width="500px">
        <template #header>
            <div class="font-bold text-base">测试翻译</div>
        </template>
        <el-form ref="formRef" :model="form" :rules="rules" label-width="auto">
            <el-form-item label="翻译供应商" prop="vendor">
                <el-select v-model="form.vendor" placeholder="请选择翻译供应商" @change="handleVendorChange">
                    <el-option v-for="item in vendorOptions" :key="item.type" :label="item.name"
                        :value="item.type"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="源语言" prop="from">
                <el-select v-model="form.from" placeholder="请选择源语言">
                    <el-option v-for="item in sourceLanguageOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="目标语言" prop="to">
                <el-select v-model="form.to" placeholder="请选择目标语言">
                    <el-option v-for="item in targetLanguageOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="源文本" prop="text">
                <el-input v-model="form.text" type="textarea" :rows="3" placeholder="请输入要翻译的文本" />
            </el-form-item>
            <el-form-item label="翻译结果" v-if="result">
                <el-input v-model="result" type="textarea" :rows="3" readonly />
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button type="primary" @click="handleTest" :loading="loading">测试</el-button>
            <el-button @click="handleCancel">取消</el-button>
        </template>
    </el-dialog>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { $message } from '@/utils/message'
import { translateAPI, VendorType, getLanguagesAPI } from '@/api/translation'
import { getVendorsAPI, type IVendorInfo } from '@/api/translation-route'

interface ILanguageOption {
    label: string
    value: string
}

const sourceLanguageOptions = ref<ILanguageOption[]>([])
const targetLanguageOptions = ref<ILanguageOption[]>([])
const vendorOptions = ref<IVendorInfo[]>([])

interface IForm {
    text: string
    from: string
    to: string
    vendor: VendorType
}

const visible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()
const result = ref('')

const form = reactive<IForm>({
    text: '',
    from: 'auto',
    to: 'en',
    vendor: VendorType.BAIDU
})

const rules = reactive<FormRules>({
    text: [
        { required: true, message: '请输入要翻译的文本', trigger: 'blur' }
    ],
    from: [
        { required: true, message: '请选择源语言', trigger: 'change' }
    ],
    to: [
        { required: true, message: '请选择目标语言', trigger: 'change' }
    ],
    vendor: [
        { required: true, message: '请选择翻译供应商', trigger: 'change' }
    ]
})

const open = async (vendor: VendorType) => {
    visible.value = true
    formRef.value?.resetFields()
    result.value = ''
    form.vendor = vendor;

    try {
        const res = await getVendorsAPI()
        vendorOptions.value = res.data
        handleVendorChange()
    } catch (error) {
        console.error('获取数据失败:', error)
        $message.error('获取数据失败')
    }
}

const handleVendorChange = async () => {
    try {
        const [source, target] = await Promise.all([
            getLanguagesAPI(form.vendor, 'source'),
            getLanguagesAPI(form.vendor, 'target'),
        ])

        sourceLanguageOptions.value = Object.entries(source.data).map(([value, label]) => ({
            label,
            value
        }))

        targetLanguageOptions.value = Object.entries(target.data).map(([value, label]) => ({
            label,
            value
        }))

        if (!sourceLanguageOptions.value.find(i => i.value === form.from)) {
            form.from = sourceLanguageOptions.value[0]?.value ?? ''
        }

        if (!targetLanguageOptions.value.find(i => i.value === form.to)) {
            form.to = targetLanguageOptions.value[0]?.value ?? ''
        }
    } catch (e) {
        sourceLanguageOptions.value = []
        targetLanguageOptions.value = []
        form.from = ''
        form.to = ''
    }
}

const handleTest = async () => {
    if (!formRef.value) return

    try {
        await formRef.value.validate()
        loading.value = true

        const res = await translateAPI({
            text: form.text,
            from: form.from,
            to: form.to,
            vendor: form.vendor
        })

        result.value = res.data.text
        $message.success('翻译成功')
    } catch (error) {
        console.error('翻译失败:', error)
        // $message.error('翻译失败')
    } finally {
        loading.value = false
    }
}

const handleCancel = () => {
    visible.value = false
    formRef.value?.resetFields()
    result.value = ''
}

defineExpose({
    open
})
</script>