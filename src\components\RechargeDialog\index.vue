<template>
    <el-dialog v-model="dialogVisible" title="积分充值" width="800" @close="handleClose" :close-on-click-modal="false">
        <el-table :data="products" class="w-full" v-loading="loading" highlight-current-row
            @current-change="handleCurrentChange">
            <el-table-column prop="name" label="套餐"></el-table-column>
            <el-table-column prop="points" label="积分数量" width="120"></el-table-column>
            <el-table-column prop="price" label="价格(元)" width="120">
                <template #default="scope">
                    {{ (scope.row.price / 100).toFixed(2) }}
                </template>
            </el-table-column>
            <el-table-column prop="bonusPoints" label="赠送积分" width="120"></el-table-column>
        </el-table>

        <div class="mt-4">
            <el-radio-group v-model="paymentMethod">
                <el-radio value="alipay">支付宝支付</el-radio>
                <el-radio value="wxpay" :disabled="true">微信支付</el-radio>
            </el-radio-group>
        </div>

        <div class="balance-info"
            style="margin-top: 20px; padding: 10px; background-color: #f5f7fa; border-radius: 4px;">
            <span>当前积分余额: {{ balance }}</span>
        </div>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleRecharge()">确认充值</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
    getPointsProductsAPI,
    getPointsBalanceAPI,
    createPointsOrderAPI,
    createPaymentAPI
} from '../../api/points'
import type { PointsProduct } from '../../api/points'

const dialogVisible = defineModel<boolean>({ required: true })

const products = ref<PointsProduct[]>([])
const balance = ref<number>(0)
const loading = ref<boolean>(false)
const paymentMethod = ref<'alipay' | 'wxpay'>('alipay')
const currentRow = ref<PointsProduct | undefined>(undefined)

// 获取积分商品列表
const fetchProducts = async () => {
    try {
        loading.value = true
        const res = await getPointsProductsAPI()
        products.value = res.data || []
    } catch (error) {
        ElMessage.error('获取积分商品失败')
    } finally {
        loading.value = false
    }
}

// 获取用户积分余额
const fetchBalance = async () => {
    try {
        const res = await getPointsBalanceAPI()
        balance.value = res.data.balance || 0
    } catch (error) {
        ElMessage.error('获取积分余额失败')
    }
}

const handleCurrentChange = (val: PointsProduct | undefined) => {
    currentRow.value = val
}

// 处理充值
const handleRecharge = async () => {
    try {
        const product = currentRow.value;
        if (!product) {
            ElMessage.error('请选择充值套餐')
            return
        }
        // 创建订单
        const orderRes = await createPointsOrderAPI({ productId: product.id })
        const order = orderRes.data

        // 创建支付链接
        const paymentRes = await createPaymentAPI({
            orderNo: order.orderNo,
            paymentMethod: 'alipay'
        })

        // 跳转到支付页面
        window.open(paymentRes.data.paymentUrl, '_blank')

    } catch (error) {
        ElMessage.error('创建订单失败')
    }
}

const handleClose = () => {
    // 清理操作
}

onMounted(() => {
    fetchProducts()
    fetchBalance()
})
</script>
