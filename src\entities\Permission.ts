import { Entity, Column, OneToMany } from 'typeorm';
import { BaseEntity } from './BaseEntity';
import { MembershipPermission } from './MembershipPermission';

/**
 * 权限实体
 */
@Entity('permissions')
export class Permission extends BaseEntity {
  @Column({
    length: 100,
    comment: '权限名称',
  })
  name!: string;

  @Column({
    length: 100,
    comment: '权限标识',
    unique: true,
  })
  key!: string;

  @Column({
    comment: '权限描述',
    type: 'text',
    nullable: true,
  })
  description?: string;

  @Column({
    comment: '权限分组',
    length: 50,
    nullable: true,
  })
  group?: string;

  @Column({
    comment: '是否启用',
    default: true,
  })
  isActive!: boolean;

  @OneToMany(
    () => MembershipPermission,
    (membershipPermission: MembershipPermission) => membershipPermission.permission,
  )
  membershipPermissions!: MembershipPermission[];
}
