API接口支付
请求URL
https://z-pay.cn/mapi.php
请求方法
POST（方式为form-data）

说明：系统已更新为使用API接口支付方式，不再使用页面跳转支付。
请求参数
字段名	变量名	必填	类型	示例值	描述
商户ID	pid	是	String	1001	
支付渠道ID	cid	否	String	1234	如果不填则随机使用某一支付渠道
支付方式	type	是	String	alipay	支付宝：alipay 微信支付：wxpay
商户订单号	out_trade_no	是	String	20160806151343349	
异步通知地址	notify_url	是	String	http://www.pay.com/notify_url.php	服务器异步通知地址
商品名称	name	是	String	VIP会员	如超过127个字节会自动截取
商品金额	money	是	String	1.00	单位：元，最大2位小数
用户IP地址	clientip	是	String	*************	用户发起支付的IP地址
设备类型	device	否	String	pc	根据当前用户浏览器的UA判断，
传入用户所使用的浏览器
或设备类型，默认为pc
业务扩展参数	param	否	String	没有请留空	支付后原样返回
签名字符串	sign	是	String	202cb962ac59075b964b07152d234b70	签名算法参考本页底部
签名类型	sign_type	是	String	MD5	默认为MD5
成功返回
字段名	变量名	类型	示例值	描述
返回状态码	code	Int	1	1为成功，其它值为失败
返回信息	msg	String		失败时返回原因
订单号	trade_no	String	20160806151343349	支付订单号
ZPAY内部订单号	O_id	String	123456	ZPAY内部订单号
支付跳转url	payurl	String	https://xxx.cn/pay/wxpay/202010903/	如果返回该字段，则直接跳转到该url支付
二维码链接	qrcode	String	https://xxx.cn/pay/wxpay/202010903/	如果返回该字段，则根据该url生成二维码
二维码图片	img	String	https://z-pay.cn/qrcode/123.jpg	该字段为付款二维码的图片地址
失败返回
{"code":"error","msg":"具体的错误信息"}