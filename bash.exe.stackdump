Stack trace:
Frame         Function      Args
0007FFFFABD0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFABD0, 0007FFFF9AD0) msys-2.0.dll+0x1FE8E
0007FFFFABD0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAEA8) msys-2.0.dll+0x67F9
0007FFFFABD0  000210046832 (000210286019, 0007FFFFAA88, 0007FFFFABD0, 000000000000) msys-2.0.dll+0x6832
0007FFFFABD0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFABD0  000210068E24 (0007FFFFABE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAEB0  00021006A225 (0007FFFFABE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCC38D0000 ntdll.dll
7FFCC1950000 KERNEL32.DLL
7FFCC0FE0000 KERNELBASE.dll
7FFCC1B20000 USER32.dll
7FFCC1410000 win32u.dll
7FFCC1A90000 GDI32.dll
7FFCC1560000 gdi32full.dll
7FFCC1440000 msvcp_win.dll
7FFCC1680000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFCC3050000 advapi32.dll
7FFCC28D0000 msvcrt.dll
7FFCC3120000 sechost.dll
7FFCC0E50000 bcrypt.dll
7FFCC3770000 RPCRT4.dll
7FFCC0BB0000 CRYPTBASE.DLL
7FFCC14E0000 bcryptPrimitives.dll
7FFCC36B0000 IMM32.DLL
