import { <PERSON><PERSON>erWindow } from "electron";
import { ProxyConfig } from "../interface";

class BaseWindowsManager {

    private windowsMap = new Map()

    createWindow(id: string, url: string, options?: Electron.BrowserWindowConstructorOptions, proxyConfig?: ProxyConfig): BrowserWindow {
        let win = this.windowsMap.get(id);
        if (!win) {
            win = new BrowserWindow(options);
            win.on("closed", () => {
                this.windowsMap.delete(id)
            });
            this.windowsMap.set(id, win)
            this.setProxy(id, proxyConfig).then(() => {
                if (url) {
                    this.loadURL(id, url)
                }
            })
        }
        return win;
    }

    public async setProxy(id: string, config?: ProxyConfig) {
        let win = this.windowsMap.get(id);
        if (win) {
            if (!config || !config.enabled) {
                // 关闭代理
                await win.webContents.session.setProxy({ mode: 'system' });
                return;
            }
            // 拼接代理规则
            const proxyRule = `${config.protocol}=${config.host}:${config.port}`;
            await win.webContents.session.setProxy({
                mode: 'fixed_servers',
                proxyRules: proxyRule
            });
            // 处理认证
            if (config.auth && config.username && config.password) {
                win.webContents.removeAllListeners('login');
                win.webContents.on('login', (event, request, authInfo, callback) => {
                    event.preventDefault();
                    callback(config.username, config.password);
                });
            }
        }
    }

    public async loadURL(id: string, url: string) {
        let win = this.windowsMap.get(id);
        if (win) {
            if (url.startsWith('http')) {
                win.loadURL(url)
            } else {
                const fileUrl = url.split('#')[0];
                const hash = url.split('#')[1];
                win.loadFile(fileUrl, { hash })
            }
        }
    }

    public getWindow(id: string) {
        return this.windowsMap.get(id);
    }

    public closeAllWindows() {
        this.windowsMap.forEach((win: BrowserWindow) => {
            win.close();
            win.destroy();
        })
    }

    public focusWindow(winId: string) {
        const win = this.windowsMap.get(winId);
        if (win) {
            win.focus();
        }
    }

    public sendMessageToRenderer(winId: string, eventName: string, ...eventData: any) {
        const win = this.windowsMap.get(winId);
        if (win) {
            win.webContents.send(eventName, ...eventData)
        }
    }
}

export { BaseWindowsManager };