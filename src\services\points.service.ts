import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import { Client } from '../entities/Client';
import {
  PointsTransaction,
  PointsTransactionType,
  PointsTransactionReferenceType,
} from '../entities/PointsTransaction';
import { VendorType, TranslationType } from '../entities/TranslationRoute';
import { getPointsConsumption } from '../config/points-consumption';

export interface ConsumePointsOptions {
  clientId: string;
  vendor: VendorType;
  type: TranslationType;
  description: string;
  referenceType?: PointsTransactionReferenceType;
  referenceId?: string;
  metadata?: Record<string, any>;
}

export interface RefundPointsOptions {
  clientId: string;
  amount: number;
  description: string;
  referenceType?: PointsTransactionReferenceType;
  referenceId?: string;
  metadata?: Record<string, any>;
}

export interface AddPointsOptions {
  clientId: string;
  amount: number;
  type: PointsTransactionType;
  description: string;
  referenceType?: PointsTransactionReferenceType;
  referenceId?: string;
  metadata?: Record<string, any>;
}

/**
 * 积分服务
 * 处理积分扣除、返还、流水记录等核心业务逻辑
 */
export class PointsService {
  private clientRepository: Repository<Client>;
  private transactionRepository: Repository<PointsTransaction>;

  constructor() {
    this.clientRepository = AppDataSource.getRepository(Client);
    this.transactionRepository = AppDataSource.getRepository(PointsTransaction);
  }

  /**
   * 获取用户积分余额
   */
  async getBalance(clientId: string): Promise<number> {
    const client = await this.clientRepository.findOne({
      where: { id: clientId },
      select: ['pointsBalance'],
    });

    if (!client) {
      throw new Error('用户不存在');
    }

    return client.pointsBalance;
  }

  /**
   * 检查用户积分是否足够
   */
  async checkBalance(clientId: string, requiredPoints: number): Promise<boolean> {
    const balance = await this.getBalance(clientId);
    return balance >= requiredPoints;
  }

  /**
   * 消耗积分（用于翻译等操作）
   */
  async consumePoints(options: ConsumePointsOptions): Promise<PointsTransaction> {
    const { clientId, vendor, type, description, referenceType, referenceId, metadata } = options;

    // 获取需要消耗的积分数量
    const requiredPoints = getPointsConsumption(vendor, type);

    // 检查余额是否足够
    const hasEnoughBalance = await this.checkBalance(clientId, requiredPoints);
    if (!hasEnoughBalance) {
      throw new Error('积分余额不足');
    }

    // 开始事务
    return await AppDataSource.transaction(async (manager) => {
      // 扣除积分
      const client = await manager.findOne(Client, { where: { id: clientId } });
      if (!client) {
        throw new Error('用户不存在');
      }

      client.pointsBalance -= requiredPoints;
      await manager.save(client);

      // 创建流水记录
      const transaction = manager.create(PointsTransaction, {
        clientId,
        type: PointsTransactionType.CONSUME,
        amount: -requiredPoints,
        balance: client.pointsBalance,
        description,
        referenceType,
        referenceId,
        metadata: {
          ...metadata,
          vendor,
          translationType: type,
          consumedPoints: requiredPoints,
        },
      });

      return await manager.save(transaction);
    });
  }

  /**
   * 退还积分（翻译失败时使用）
   */
  async refundPoints(options: RefundPointsOptions): Promise<PointsTransaction> {
    const { clientId, amount, description, referenceType, referenceId, metadata } = options;

    if (amount <= 0) {
      throw new Error('退还积分数量必须大于0');
    }

    // 开始事务
    return await AppDataSource.transaction(async (manager) => {
      // 增加积分
      const client = await manager.findOne(Client, { where: { id: clientId } });
      if (!client) {
        throw new Error('用户不存在');
      }

      client.pointsBalance += amount;
      await manager.save(client);

      // 创建流水记录
      const transaction = manager.create(PointsTransaction, {
        clientId,
        type: PointsTransactionType.REFUND,
        amount: amount,
        balance: client.pointsBalance,
        description,
        referenceType,
        referenceId,
        metadata,
      });

      return await manager.save(transaction);
    });
  }

  /**
   * 增加积分（充值、赠送等）
   */
  async addPoints(options: AddPointsOptions): Promise<PointsTransaction> {
    const { clientId, amount, type, description, referenceType, referenceId, metadata } = options;

    if (amount <= 0) {
      throw new Error('增加积分数量必须大于0');
    }

    // 开始事务
    return await AppDataSource.transaction(async (manager) => {
      // 增加积分
      const client = await manager.findOne(Client, { where: { id: clientId } });
      if (!client) {
        throw new Error('用户不存在');
      }

      client.pointsBalance += amount;
      await manager.save(client);

      // 创建流水记录
      const transaction = manager.create(PointsTransaction, {
        clientId,
        type,
        amount: amount,
        balance: client.pointsBalance,
        description,
        referenceType,
        referenceId,
        metadata,
      });

      return await manager.save(transaction);
    });
  }

  /**
   * 获取用户积分流水记录
   */
  async getTransactions(
    clientId: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<{ transactions: PointsTransaction[]; total: number }> {
    const [transactions, total] = await this.transactionRepository.findAndCount({
      where: { clientId },
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return { transactions, total };
  }

  /**
   * 根据引用查找流水记录
   */
  async getTransactionByReference(
    referenceType: PointsTransactionReferenceType,
    referenceId: string,
  ): Promise<PointsTransaction[]> {
    return await this.transactionRepository.find({
      where: { referenceType, referenceId },
      order: { createdAt: 'DESC' },
    });
  }
}
