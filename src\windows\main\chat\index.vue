<template>
    <div class="flex h-full pb-1">
        <div class="w-48 p-2 h-full flex-shrink-0 border-r bg-white rounded-lg flex flex-col">
            <div class="flex-shrink-0">
                <!-- <el-button class="w-full" type="primary" @click="createChatAccount()">新建会话</el-button> -->
                <el-dropdown class="w-full" trigger="click" @command="createChatAccount">
                    <el-button class="w-full" type="primary">
                        新建会话
                    </el-button>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item :command="PlatformKeys.Telegram">
                                <div class="flex items-center">
                                    <PlatFormIcon :platform="PlatformKeys.Telegram" size="16"></PlatFormIcon>
                                    <span class="ml-2">Telegram</span>
                                </div>
                            </el-dropdown-item>
                            <el-dropdown-item :command="PlatformKeys.WhatsApp" disabled>
                                <div class="flex items-center">
                                    <PlatFormIcon :platform="PlatformKeys.WhatsApp" size="16"></PlatFormIcon>
                                    <span class="ml-2">WhatsApp</span>
                                </div>
                            </el-dropdown-item>
                            <el-dropdown-item :command="PlatformKeys.Facebook" disabled>
                                <div class="flex items-center">
                                    <PlatFormIcon :platform="PlatformKeys.Facebook" size="16"></PlatFormIcon>
                                    <span class="ml-2">Facebook</span>
                                </div>
                            </el-dropdown-item>
                            <el-dropdown-item :command="PlatformKeys.LINE" disabled>
                                <div class="flex items-center">
                                    <PlatFormIcon :platform="PlatformKeys.LINE" size="16"></PlatFormIcon>
                                    <span class="ml-2">LINE</span>
                                </div>
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
            <el-scrollbar class="py-2 flex-grow overflow-y-auto bg-white">
                <div v-for="(item, index) in chatList" :key="item.id" @click="showChat(item.id)"
                    class="flex p-2 cursor-pointer relative group hover:bg-[#f1f3f5] rounded-md mb-2"
                    :class="selectedChatId === item.id ? 'bg-[#e9ecf0]' : ''">
                    <div class="absolute top-1 right-2">
                        <el-dropdown placement="bottom-end" size="small">
                            <div>
                                <MoreOutlined class="size-5"></MoreOutlined>
                            </div>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item @click.stop="closeChat(item.id)">关闭</el-dropdown-item>
                                    <el-dropdown-item @click.stop="deleteChat(item.id)">删除</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                    <div class="relative flex-shrink-0">
                        <img :src="item.avatar || userAvatar" class="size-10 rounded-full" />
                        <div v-if="item?.unreadCount"
                            class="absolute flex items-center justify-center h-[14px] min-w-[14px] text-white bg-red-600 rounded-full top-[-5px] left-[-5px]">
                            <div class="text-xs scale-75">{{
                                formatUnreadCount(item.unreadCount) }}</div>
                        </div>
                        <div class="absolute bottom-0 right-0 bg-white rounded-full overflow-hidden p-[2px]">
                            <PlatFormIcon :platform="item.platform" size="12"></PlatFormIcon>
                        </div>
                    </div>
                    <div class="px-2 flex-grow">
                        <div class="text-sm font-bold line-clamp-1 pr-2">{{ item?.nickname
                            ||
                            item.platform }}</div>
                        <div class="text-xs text-slate-500 line-clamp-1">{{ item?.accountId
                            || '请先登录' }}
                        </div>
                    </div>
                </div>
            </el-scrollbar>
        </div>
        <div class="h-full flex-grow rounded-lg ml-1">
            <ChatView ref="chatViewRef" @sizeChange="handleSizeChange">
                <LoadingView v-if="currentChatInfo?.isLoading"></LoadingView>
                <div v-else-if="currentChatInfo?.isLoadFail"
                    class="h-full w-full flex justify-center bg-white items-center rounded-md">
                    <el-button type="primary" @click="reloadChat(selectedChatId)">重新加载</el-button>
                </div>
                <div v-else-if="currentChatInfo?.isCreated === false || currentChatInfo?.isLoaded === false"
                    class="h-full w-full flex justify-center items-center bg-white rounded-md">
                    <el-button type="primary"
                        @click="createChatWindow(currentChatInfo!.id, currentChatInfo!.platform)">打开会话</el-button>
                </div>
            </ChatView>
        </div>
        <div>
            <ToolsBar :chatId="selectedChatId" :chatInfo="currentChatInfo"></ToolsBar>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { computed, ref } from 'vue';
import ToolsBar from './components/ToolsBar/index.vue'
import PlatFormIcon from '@/components/PlatFormIcon/index.vue'
import { PlatformKeys } from '../../../constants/enum';
import ChatView from './components/ChatView.vue'
import LoadingView from './components/LoadingView.vue'
import { Rectangle } from 'electron';
import { onMounted } from 'vue';
import { useChatStore } from '../../../stores/chat'
import { MoreOutlined } from '@vicons/antd'
import userAvatar from '../../../assets/images/user-avatar.png'
const chatStore = useChatStore();
const chatViewRef = ref();
const selectedChatId = computed(() => chatStore.selectedChatId)
const chatList = computed(() => chatStore.chatList)
const currentChatInfo = computed(() => chatList.value.find((i: { id: string; }) => i.id === selectedChatId.value))

const createChatWindow = (id: string, platform: string) => {
    chatStore.createChatWindow(id, platform, chatViewRef.value!.getBounds())
}

const createChatAccount = async (command: PlatformKeys) => {
    chatStore.createChatAccount(command);
}

const handleSizeChange = (bounds: Rectangle) => {
    window.ipcRenderer?.invoke('setChatWindowBounds', bounds)
}

const showChat = async (id: string) => {
    chatStore.showChat(id)
}

const closeChat = (id: string) => {
    chatStore.closeChat(id)
}

const deleteChat = (id: string) => {
    chatStore.deleteChat(id)
}

const reloadChat = (id: string) => {
    chatStore.reloadChat(id)
}

const formatUnreadCount = (number: number) => {
    return number > 99 ? `99+` : `${number}`
}

onMounted(async () => {
    chatStore.setSelectedChatId(chatList.value[0]?.id || '')
})

</script>