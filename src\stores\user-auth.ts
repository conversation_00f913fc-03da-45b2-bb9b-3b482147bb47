// Pinia Store
import { defineStore } from 'pinia'
import { loginAPI } from '../api/auth'
import { setToken, removeToken } from '../utils/auth'
import { $message, $confirm } from '../utils/message'
import { toLoginWindow } from '../utils/windows';

interface State {
    loginTime: string | Date
}

interface AppUserLogin {
    email: string
    password: string
    captchaAnswer?: string
    captchaId?: string
}

export const useUserAuthStore = defineStore('user/auth', {
    // 转换为函数
    state: (): State => ({
        loginTime: '',
    }),
    getters: {

    },
    actions: {
        login(params: AppUserLogin) {
            return loginAPI(params).then((data: any) => {
                this.loginTime = new Date();
                setToken(data.data.token);
                return data.data;
            })
        },
        logout() {
            $confirm('确定退出登录吗', '温馨提示', {
                showClose: false,
            }).then(() => {
                $message.success('退出成功')
                removeToken();
                setTimeout(() => {
                    toLoginWindow();
                }, 1500)
            })
        }
    },
})