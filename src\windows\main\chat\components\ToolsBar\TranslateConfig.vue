<template>
    <div class="w-fit h-full pl-1">
        <div class="bg-white rounded-lg w-[300px] h-full p-3 flex flex-col">
            <div class="font-bold flex-shrink-0">翻译设置</div>
            <div class="pt-4 flex-grow h-0 overflow-auto scrollbar-hide">
                <div>
                    <el-form :model="form" label-width="auto" label-position="top">
                        <el-form-item label="聊天文本翻译" label-position="left">
                            <div class="w-full flex justify-end">
                                <el-switch v-model="form.translateChatRecord" />
                            </div>
                        </el-form-item>
                        <template v-if="form.translateChatRecord">
                            <el-form-item label="翻译线路" label-position="right">
                                <el-select v-model="form.chatRecordTranslateLine" placeholder="请选择翻译线路">
                                    <el-option v-for="item in textTranslateLines" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="源语言" label-position="right">
                                <el-select v-model="form.chatRecordTranslateSource" placeholder="请选择源语言">
                                    <el-option v-for="item in chatRecordSourceLanguages" :key="item.value"
                                        :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="目标语言" label-position="right">
                                <el-select v-model="form.chatRecordTranslateTarget" placeholder="请选择目标语言">
                                    <el-option v-for="item in chatRecordTargetLanguages" :key="item.value"
                                        :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </template>
                        <el-form-item label="聊天图片翻译" label-position="left">
                            <div class="w-full flex justify-end">
                                <el-switch v-model="form.translateImage" />
                            </div>
                        </el-form-item>
                        <template v-if="form.translateImage">
                            <el-form-item label="翻译线路" label-position="right">
                                <el-select v-model="form.imageTranslateLine" placeholder="请选择翻译线路">
                                    <el-option v-for="item in imageTranslateLines" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="源语言" label-position="right">
                                <el-select v-model="form.imageTranslateSource" placeholder="请选择源语言" class="flex-1">
                                    <el-option v-for="item in imageSourceLanguages" :key="item.value"
                                        :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="目标语言" label-position="right">
                                <el-select v-model="form.imageTranslateTarget" placeholder="请选择目标语言">
                                    <el-option v-for="item in imageTargetLanguages" :key="item.value"
                                        :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </template>
                        <el-form-item label="输入框文本翻译" label-position="left">
                            <div class="w-full flex justify-end">
                                <el-switch v-model="form.translateInput" />
                            </div>
                        </el-form-item>
                        <template v-if="form.translateInput">
                            <el-form-item label="翻译线路" label-position="right">
                                <el-select v-model="form.inputTranslateLine" placeholder="请选择翻译线路">
                                    <el-option v-for="item in textTranslateLines" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="源语言" label-position="right">
                                <el-select v-model="form.inputTranslateSource" placeholder="请选择源语言">
                                    <el-option v-for="item in inputSourceLanguages" :key="item.value"
                                        :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="目标语言" label-position="right">
                                <el-select v-model="form.inputTranslateTarget" placeholder="请选择目标语言">
                                    <el-option v-for="item in inputTargetLanguages" :key="item.value"
                                        :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </template>
                        <el-form-item label="聊天语音翻译" label-position="left">
                            <div class="w-full flex justify-end">
                                <el-switch disabled v-model="form.translateAudio" />
                            </div>
                        </el-form-item>
                        <template v-if="form.translateAudio">
                            <el-form-item label="翻译线路" label-position="right">
                                <el-select v-model="form.audioTranslateLine" placeholder="请选择翻译线路">
                                    <el-option v-for="item in audioTranslateLines" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="源语言" label-position="right">
                                <el-select v-model="form.audioTranslateSource" placeholder="请选择源语言">
                                    <el-option v-for="item in audioSourceLanguages" :key="item.value"
                                        :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="目标语言" label-position="right">
                                <el-select v-model="form.audioTranslateTarget" placeholder="请选择目标语言">
                                    <el-option v-for="item in audioTargetLanguages" :key="item.value"
                                        :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </template>
                    </el-form>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { getTranslationRouteListAPI, ITranslationRoute, TranslationType, VendorType } from '@/api/translation-route'
import { getLanguagesAPI } from '@/api/translation'
import { $message } from '@/utils/message'
import { VENDOR_LABELS } from '@/constants/translation-route'

const STORAGE_KEY = 'translate_config'

interface TranslateLineOption {
    label: string
    value: string
}

interface TranslateConfig {
    // 聊天记录翻译
    translateChatRecord: boolean
    chatRecordTranslateLine: string
    chatRecordTranslateSource: string
    chatRecordTranslateTarget: string

    // 输入框翻译
    translateInput: boolean
    inputTranslateLine: string
    inputTranslateSource: string
    inputTranslateTarget: string

    // 图片翻译
    translateImage: boolean
    imageTranslateLine: string
    imageTranslateSource: string
    imageTranslateTarget: string

    // 语音翻译
    translateAudio: boolean
    audioTranslateLine: string
    audioTranslateSource: string
    audioTranslateTarget: string

}

const props = defineProps<{
    chatId: string
}>()

const isReady = ref(false);

// 翻译线路列表
const textTranslateLines = ref<TranslateLineOption[]>([])
const imageTranslateLines = ref<TranslateLineOption[]>([])
const audioTranslateLines = ref<TranslateLineOption[]>([])

// 语言列表
const chatRecordSourceLanguages = ref<TranslateLineOption[]>([])
const chatRecordTargetLanguages = ref<TranslateLineOption[]>([])
const inputSourceLanguages = ref<TranslateLineOption[]>([])
const inputTargetLanguages = ref<TranslateLineOption[]>([])
const imageSourceLanguages = ref<TranslateLineOption[]>([])
const imageTargetLanguages = ref<TranslateLineOption[]>([])
const audioSourceLanguages = ref<TranslateLineOption[]>([])
const audioTargetLanguages = ref<TranslateLineOption[]>([])

// 从本地缓存读取配置
const loadConfig = async (): Promise<TranslateConfig> => {
    try {
        // 从 Electron 获取配置
        const config = await window.ipcRenderer.invoke('get-translate-config', props.chatId)
        if (config) {
            return config as TranslateConfig
        }
    } catch (error) {
        console.error('从 Electron 获取配置失败:', error)
    }

    // 默认配置
    return {
        translateChatRecord: false,
        translateInput: false,
        translateImage: false,
        translateAudio: false,
        chatRecordTranslateLine: '',
        chatRecordTranslateSource: '',
        chatRecordTranslateTarget: '',
        inputTranslateLine: '',
        inputTranslateSource: '',
        inputTranslateTarget: '',
        imageTranslateLine: '',
        imageTranslateSource: '',
        imageTranslateTarget: '',
        audioTranslateLine: '',
        audioTranslateSource: '',
        audioTranslateTarget: '',
    }
}

const defaultConfig: TranslateConfig = {
    translateChatRecord: false,
    translateInput: false,
    translateImage: false,
    translateAudio: false,
    chatRecordTranslateLine: '',
    chatRecordTranslateSource: '',
    chatRecordTranslateTarget: '',
    inputTranslateLine: '',
    inputTranslateSource: '',
    inputTranslateTarget: '',
    imageTranslateLine: '',
    imageTranslateSource: '',
    imageTranslateTarget: '',
    audioTranslateLine: '',
    audioTranslateSource: '',
    audioTranslateTarget: '',
}

const form = ref<TranslateConfig>(defaultConfig)

// 获取翻译线路列表
const fetchTranslationRoutes = async () => {
    try {
        const res = await getTranslationRouteListAPI()
        const routes = res.data;

        // 按类型分类
        textTranslateLines.value = routes
            .filter((item: ITranslationRoute) => item.type === TranslationType.TEXT && item.isActive)
            .map((item: ITranslationRoute) => ({
                label: VENDOR_LABELS[item.vendor],
                value: item.vendor
            }))

        imageTranslateLines.value = routes
            .filter((item: ITranslationRoute) => item.type === TranslationType.IMAGE && item.isActive)
            .map((item: ITranslationRoute) => ({
                label: VENDOR_LABELS[item.vendor],
                value: item.vendor
            }))

        audioTranslateLines.value = routes
            .filter((item: ITranslationRoute) => item.type === TranslationType.AUDIO && item.isActive)
            .map((item: ITranslationRoute) => ({
                label: VENDOR_LABELS[item.vendor],
                value: item.vendor
            }))

        // 检查并设置默认翻译线路
        // 聊天记录翻译
        if (form.value.translateChatRecord) {
            const hasSelectedLine = textTranslateLines.value.some(line => line.value === form.value.chatRecordTranslateLine)
            form.value.chatRecordTranslateLine = hasSelectedLine ? form.value.chatRecordTranslateLine : (textTranslateLines.value[0]?.value || '')
        }

        // 输入框翻译
        if (form.value.translateInput) {
            const hasSelectedLine = textTranslateLines.value.some(line => line.value === form.value.inputTranslateLine)
            form.value.inputTranslateLine = hasSelectedLine ? form.value.inputTranslateLine : (textTranslateLines.value[0]?.value || '')
        }

        // 图片翻译
        if (form.value.translateImage) {
            const hasSelectedLine = imageTranslateLines.value.some(line => line.value === form.value.imageTranslateLine)
            form.value.imageTranslateLine = hasSelectedLine ? form.value.imageTranslateLine : (imageTranslateLines.value[0]?.value || '')
        }

        // 语音翻译
        if (form.value.translateAudio) {
            const hasSelectedLine = audioTranslateLines.value.some(line => line.value === form.value.audioTranslateLine)
            form.value.audioTranslateLine = hasSelectedLine ? form.value.audioTranslateLine : (audioTranslateLines.value[0]?.value || '')
        }
    } catch (error) {
        console.error('获取翻译线路列表失败:', error)
        $message.error('获取翻译线路列表失败')
    }
}

// 获取语言列表
const fetchLanguages = async (vendor: VendorType, type: 'source' | 'target') => {
    try {
        const res = await getLanguagesAPI(vendor, type)
        return Object.entries(res.data).map(([value, label]) => ({
            label: label as string,
            value
        }))
    } catch (error) {
        console.error('获取语言列表失败:', error)
        $message.error('获取语言列表失败')
        return []
    }
}

// 初始化语言列表
const initLanguages = async () => {
    // 聊天记录翻译
    if (form.value.chatRecordTranslateLine) {
        chatRecordSourceLanguages.value = await fetchLanguages(form.value.chatRecordTranslateLine as VendorType, 'source')
        chatRecordTargetLanguages.value = await fetchLanguages(form.value.chatRecordTranslateLine as VendorType, 'target')
    }

    // 输入框翻译
    if (form.value.inputTranslateLine) {
        inputSourceLanguages.value = await fetchLanguages(form.value.inputTranslateLine as VendorType, 'source')
        inputTargetLanguages.value = await fetchLanguages(form.value.inputTranslateLine as VendorType, 'target')
    }

    // 图片翻译
    if (form.value.imageTranslateLine) {
        imageSourceLanguages.value = await fetchLanguages(form.value.imageTranslateLine as VendorType, 'source')
        imageTargetLanguages.value = await fetchLanguages(form.value.imageTranslateLine as VendorType, 'target')
    }

    // 语音翻译
    if (form.value.audioTranslateLine) {
        audioSourceLanguages.value = await fetchLanguages(form.value.audioTranslateLine as VendorType, 'source')
        audioTargetLanguages.value = await fetchLanguages(form.value.audioTranslateLine as VendorType, 'target')
    }
}

const init = async () => {
    form.value = await loadConfig()
    await fetchTranslationRoutes()
    await initLanguages()
    isReady.value = true;
}

// 监听线路变化，获取对应的语言列表
watch(() => form.value.chatRecordTranslateLine, async (newVal) => {
    if (newVal && isReady.value) {
        chatRecordSourceLanguages.value = await fetchLanguages(newVal as VendorType, 'source')
        chatRecordTargetLanguages.value = await fetchLanguages(newVal as VendorType, 'target')
        // 设置默认值为第一个选项，如果列表为空则置空
        form.value.chatRecordTranslateSource = chatRecordSourceLanguages.value[0]?.value || ''
        form.value.chatRecordTranslateTarget = chatRecordTargetLanguages.value[0]?.value || ''
    }
})

watch(() => form.value.inputTranslateLine, async (newVal) => {
    if (newVal && isReady.value) {
        inputSourceLanguages.value = await fetchLanguages(newVal as VendorType, 'source')
        inputTargetLanguages.value = await fetchLanguages(newVal as VendorType, 'target')
        // 设置默认值为第一个选项，如果列表为空则置空
        form.value.inputTranslateSource = inputSourceLanguages.value[0]?.value || ''
        form.value.inputTranslateTarget = inputTargetLanguages.value[0]?.value || ''
    }
})

watch(() => form.value.imageTranslateLine, async (newVal) => {
    if (newVal && isReady.value) {
        imageSourceLanguages.value = await fetchLanguages(newVal as VendorType, 'source')
        imageTargetLanguages.value = await fetchLanguages(newVal as VendorType, 'target')
        // 设置默认值为第一个选项，如果列表为空则置空
        form.value.imageTranslateSource = imageSourceLanguages.value[0]?.value || ''
        form.value.imageTranslateTarget = imageTargetLanguages.value[0]?.value || ''
    }
})

watch(() => form.value.audioTranslateLine, async (newVal) => {
    if (newVal && isReady.value) {
        audioSourceLanguages.value = await fetchLanguages(newVal as VendorType, 'source')
        audioTargetLanguages.value = await fetchLanguages(newVal as VendorType, 'target')
        // 设置默认值为第一个选项，如果列表为空则置空
        form.value.audioTranslateSource = audioSourceLanguages.value[0]?.value || ''
        form.value.audioTranslateTarget = audioTargetLanguages.value[0]?.value || ''
    }
})

// 监听配置变化，保存到 Electron
watch(() => form.value, async (newVal) => {
    try {
        if (isReady.value) {
            // 转换为纯对象
            const config = JSON.parse(JSON.stringify(newVal))
            await window.ipcRenderer.invoke('set-translate-config', props.chatId, config)
        }
    } catch (error) {
        console.error('保存配置到 Electron 失败:', error)
        $message.error('保存配置失败')
    }
}, { deep: true })

watch(() => props.chatId, async (newVal) => {
    if (newVal) {
        init();
    }
})

onMounted(async () => {
    init();
})
</script>

<style lang="scss" scoped>
/* 自定义滚动条隐藏 */
.scrollbar-hide::-webkit-scrollbar {
    display: none;
}
</style>