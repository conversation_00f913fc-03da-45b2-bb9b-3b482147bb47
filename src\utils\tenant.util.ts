import { FindOptionsWhere } from 'typeorm';
import { BaseEntity } from '../entities/BaseEntity';

// 使用AsyncLocalStorage存储租户上下文信息
const tenantContext: { tenantId: string | null } = {
  tenantId: null,
};

/**
 * 设置当前租户ID
 */
export const setCurrentTenantId = (tenantId: string): void => {
  tenantContext.tenantId = tenantId;
};

/**
 * 获取当前租户ID
 */
export const getCurrentTenantId = (): string => {
  return tenantContext.tenantId || '0'; // 默认为0（系统级）
};

/**
 * 清除当前租户ID
 */
export const clearCurrentTenantId = (): void => {
  tenantContext.tenantId = null;
};

/**
 * 向查询条件中添加租户条件
 * @param where 原始查询条件
 * @returns 添加租户条件后的查询条件
 */
export const addTenantCondition = <T extends BaseEntity>(
  where: FindOptionsWhere<T> | FindOptionsWhere<T>[] | undefined,
): FindOptionsWhere<T> | FindOptionsWhere<T>[] => {
  const tenantId = getCurrentTenantId();

  // 系统级账号（tenantId === '0'）可以查看所有租户的数据
  if (tenantId === '0') {
    return where || {};
  }

  // 非系统级账号只能查看自己租户的数据
  if (!where) {
    return { tenantId } as unknown as FindOptionsWhere<T>;
  }

  if (Array.isArray(where)) {
    return where.map((w) => ({ ...w, tenantId }) as unknown as FindOptionsWhere<T>);
  }

  return { ...where, tenantId } as unknown as FindOptionsWhere<T>;
};
