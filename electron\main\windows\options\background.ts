import { PRELOAD } from "../../config/config";
export default {
    title: "Background window",
    width: 0,
    height: 0,
    minWidth: 0,
    minHeight: 0,
    // 创建无边框窗口
    show: true,
    transparent: true,//透明窗口
    backgroundColor: '#00000000',//窗口底色为透明色
    frame: false,//是否无边框
    resizable: false,
    webPreferences: {
        preload: PRELOAD,
        // Warning: Enable nodeIntegration and disable contextIsolation is not secure in production
        // Consider using contextBridge.exposeInMainWorld
        // Read more on https://www.electronjs.org/docs/latest/tutorial/context-isolation
        nodeIntegration: true,
    },
}