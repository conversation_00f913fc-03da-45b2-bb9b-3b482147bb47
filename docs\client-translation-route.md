# 客户端翻译路由模块 API 文档

## 概述

客户端翻译路由模块提供翻译路由配置管理功能，包括创建、更新、删除、查询翻译路由，以及状态切换等操作。

**基础路径:** `/api/client/translation-routes`

## 认证说明

所有接口都需要在请求头中携带认证令牌：`Authorization: Bearer {token}`

## 接口列表

### 1. 创建翻译路由

创建新的翻译路由配置。

- **URL:** `/api/client/translation-routes/create`
- **方法:** `POST`
- **权限:** 需要用户认证
- **请求体:**

```json
{
  "type": "text",
  "vendor": "baidu",
  "apiKey": "your_api_key",
  "apiSecret": "your_api_secret",
  "isActive": true,
  "sort": 1,
  "accountId": "user_001"
}
```

**请求参数说明:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| type | string | 是 | 翻译类型（text/image/audio/video） |
| vendor | string | 是 | 翻译供应商（baidu/youdao） |
| apiKey | string | 是 | API密钥 |
| apiSecret | string | 否 | API密钥（部分供应商需要） |
| isActive | boolean | 否 | 是否启用，默认为true |
| sort | number | 否 | 排序值，默认为1 |
| accountId | string | 是 | 账户ID |

- **成功响应:**

```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": "route_001",
    "type": "text",
    "vendor": "baidu",
    "apiKey": "your_api_key",
    "apiSecret": "your_api_secret",
    "isActive": true,
    "sort": 1,
    "accountId": "user_001",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### 2. 获取翻译路由列表

获取当前用户的翻译路由列表。

- **URL:** `/api/client/translation-routes/list`
- **方法:** `GET`
- **权限:** 需要用户认证

- **成功响应:**

```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": "route_001",
      "type": "text",
      "vendor": "baidu",
      "apiKey": "your_api_key",
      "apiSecret": "your_api_secret",
      "isActive": true,
      "sort": 1,
      "accountId": "user_001",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    },
    {
      "id": "route_002",
      "type": "image",
      "vendor": "youdao",
      "apiKey": "your_youdao_key",
      "apiSecret": "your_youdao_secret",
      "isActive": true,
      "sort": 2,
      "accountId": "user_001",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

### 3. 更新翻译路由

更新指定的翻译路由配置。

- **URL:** `/api/client/translation-routes/update/{id}`
- **方法:** `PUT`
- **权限:** 需要用户认证
- **路径参数:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | string | 是 | 翻译路由ID |

- **请求体:**

```json
{
  "apiKey": "new_api_key",
  "apiSecret": "new_api_secret",
  "isActive": false,
  "sort": 2
}
```

**请求参数说明:** 所有字段均为可选，只更新提供的字段

- **成功响应:**

```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": "route_001",
    "type": "text",
    "vendor": "baidu",
    "apiKey": "new_api_key",
    "apiSecret": "new_api_secret",
    "isActive": false,
    "sort": 2,
    "accountId": "user_001",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-02T00:00:00.000Z"
  }
}
```

### 4. 切换路由状态

切换翻译路由的启用/禁用状态。

- **URL:** `/api/client/translation-routes/toggle-status/{id}`
- **方法:** `PUT`
- **权限:** 需要用户认证
- **路径参数:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | string | 是 | 翻译路由ID |

- **成功响应:**

```json
{
  "code": 200,
  "message": "状态切换成功",
  "data": {
    "id": "route_001",
    "type": "text",
    "vendor": "baidu",
    "apiKey": "your_api_key",
    "apiSecret": "your_api_secret",
    "isActive": false,
    "sort": 1,
    "accountId": "user_001",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-02T00:00:00.000Z"
  }
}
```

### 5. 删除翻译路由

删除指定的翻译路由。

- **URL:** `/api/client/translation-routes/delete/{id}`
- **方法:** `DELETE`
- **权限:** 需要用户认证
- **路径参数:**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | string | 是 | 翻译路由ID |

- **成功响应:**

```json
{
  "code": 200,
  "message": "删除成功"
}
```

### 6. 获取支持的供应商列表

获取系统支持的翻译供应商列表。

- **URL:** `/api/client/translation-routes/vendors`
- **方法:** `GET`
- **权限:** 需要用户认证

- **成功响应:**

```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "type": "baidu",
      "name": "百度翻译",
      "description": "百度翻译API服务",
      "icon": "https://example.com/baidu-icon.png",
      "website": "https://fanyi-api.baidu.com/"
    },
    {
      "type": "youdao",
      "name": "有道翻译",
      "description": "有道翻译API服务",
      "icon": "https://example.com/youdao-icon.png",
      "website": "https://ai.youdao.com/"
    }
  ]
}
```

## 翻译类型说明

| 类型 | 描述 | 支持的供应商 |
|------|------|-------------|
| text | 文本翻译 | baidu, youdao |
| image | 图片翻译 | youdao, alibaba |
| audio | 音频翻译 | 暂不支持 |
| video | 视频翻译 | 暂不支持 |

## 翻译供应商说明

| 供应商 | 名称 | 支持类型 | 是否需要密钥 |
|--------|------|----------|-------------|
| baidu | 百度翻译 | text | apiKey + apiSecret |
| youdao | 有道翻译 | text, image | apiKey + apiSecret |
| alibaba | 阿里云翻译 | image | apiKey + apiSecret |

## 错误码说明

| 错误码 | 描述 |
|--------|------|
| 400 | 请求参数错误或验证失败 |
| 401 | 未授权或认证失败 |
| 403 | 权限不足 |
| 404 | 翻译路由不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 每个用户可以创建多个翻译路由，用于负载均衡和备用
2. API密钥信息会被加密存储，请妥善保管
3. 翻译路由的排序值用于确定使用优先级，数值越小优先级越高
4. 禁用的路由不会被翻译服务使用
5. 删除翻译路由是永久性操作，请谨慎操作
6. 不同供应商的API密钥格式和要求可能不同
7. 建议为每种翻译类型配置至少一个可用路由
8. 翻译路由配置错误可能导致翻译服务不可用
