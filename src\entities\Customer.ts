import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from './BaseEntity';

/**
 * 客户实体
 */
@Entity('customers')
export class Customer extends BaseEntity {
  @Column({
    length: 100,
    comment: '客户名称',
  })
  @Index()
  name!: string;

  @Column({
    length: 20,
    nullable: true,
    comment: '联系电话',
  })
  phone?: string;

  @Column({
    nullable: true,
    comment: '电子邮箱',
  })
  email?: string;

  @Column({
    nullable: true,
    comment: '联系地址',
  })
  address?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: '备注信息',
  })
  remarks?: string;

  @Column({
    default: true,
    comment: '是否启用',
  })
  isActive!: boolean;
}
