import {
  <PERSON>tity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BaseEntity } from './BaseEntity';

/**
 * 客户端用户实体
 */
@Entity('clients')
export class Client extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @CreateDateColumn({ type: 'datetime' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'datetime' })
  updatedAt: Date;

  @Column({ length: 128 })
  name: string;

  @Column({ unique: true })
  email: string;

  @Column({ select: false })
  password: string;

  @Column({ nullable: true, length: 20 })
  phone?: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ nullable: true, select: false })
  resetPasswordToken?: string;

  @Column({ nullable: true, select: false })
  resetPasswordExpires?: Date;

  @Column({ type: 'int', default: 0, comment: '积分余额' })
  pointsBalance: number;
}
