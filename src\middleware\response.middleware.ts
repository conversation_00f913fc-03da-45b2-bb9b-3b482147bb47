import { Context, Next } from 'koa';
import { ResponseResult } from '../types/common.interface';

// 定义响应类型
interface ResponseBody {
  code?: number;
  message?: string;
  data?: any;
}

export const responseMiddleware = async (ctx: Context, next: Next) => {
  try {
    await next();

    // 如果已经设置了响应，不再处理
    if (ctx.body && (ctx.body as ResponseBody).code !== undefined) {
      return;
    }

    // 设置默认成功响应
    if (!ctx.body) {
      ctx.body = {
        code: 200,
        message: '成功',
      };
    } else {
      ctx.body = {
        code: 200,
        message: '成功',
        data: ctx.body,
      };
    }
  } catch (error: any) {
    // 统一错误处理
    const status = error.status || 500;
    const message = error.message || '服务器内部错误';

    ctx.status = status;
    ctx.body = {
      code: status,
      message: message,
    } as ResponseResult;

    // 输出错误日志
    // eslint-disable-next-line no-console
    console.error(`[Error] ${status} - ${message}`, error);
  }
};
