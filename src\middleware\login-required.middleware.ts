import { Context, Next } from 'koa';
import jwt from 'jsonwebtoken';
import { JWT_SECRET } from '../config/jwt';

/**
 * 检查用户是否已登录
 */
export const requireLogin = async (ctx: Context, next: Next): Promise<void> => {
  try {
    // 获取token
    const token = extractToken(ctx);

    if (!token) {
      ctx.status = 401;
      ctx.body = {
        code: 401,
        message: '请先登录',
      };
      return;
    }

    // 验证token
    const decoded = jwt.verify(token, JWT_SECRET);

    // 将用户信息添加到ctx.state
    ctx.state.user = decoded;

    await next();
  } catch (error) {
    ctx.status = 401;
    ctx.body = {
      code: 401,
      message: '登录已过期，请重新登录',
    };
  }
};

/**
 * 从请求头中提取token
 */
function extractToken(ctx: Context): string | null {
  const authHeader = ctx.headers.authorization;

  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  return null;
}
