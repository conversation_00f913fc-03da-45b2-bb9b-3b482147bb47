import Router from '@koa/router';
import ChatAccountController from '../../controllers/client/chat-account.controller';
import { clientAuthMiddleware } from '../../middleware/auth.middleware';

const router = new Router({ prefix: '/api/client/chat' });
const controller = new ChatAccountController();

// 应用认证中间件
router.use(clientAuthMiddleware);

// 注册路由
router.post('/accounts', controller.create);
router.put('/accounts/:id', controller.update);
router.delete('/accounts/:id', controller.delete);
router.get('/accounts/:id', controller.findOne);
router.get('/accounts', controller.findAll);

export default router;
