import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from './BaseEntity';

/**
 * 角色实体
 * 用于管理子账号的权限
 */
@Entity('roles')
export class Role extends BaseEntity {
  @Column({
    length: 50,
    comment: '角色名称',
  })
  @Index()
  name!: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: '角色描述',
  })
  description?: string;

  @Column({
    type: 'json',
    comment: '权限配置',
  })
  permissions!: Record<string, boolean>;

  @Column({
    default: true,
    comment: '是否启用',
  })
  isActive!: boolean;
}
