import { Context, Next } from 'koa';
import { verifyToken } from '../utils/account-auth.util';
import { AccountJwtPayload } from '../types/auth.interface';
import { setCurrentTenantId } from '../utils/tenant.util';

// 客户端JWT载荷接口
interface ClientJwtPayload {
  id: string;
  email: string;
  name: string;
  type: 'client';
  tenantId: string;
  iat: number;
  exp: number;
}

/**
 * 管理端JWT验证中间件
 * 只允许管理员账号访问
 */
export const adminAuthMiddleware = async (ctx: Context, next: Next) => {
  try {
    const token = getTokenFromHeader(ctx);
    if (!token) {
      ctx.status = 401;
      ctx.body = { code: 401, message: '未提供认证令牌' };
      return;
    }

    // 验证令牌
    const payload = verifyToken<AccountJwtPayload>(token);

    // 验证是否为管理员账号
    if (payload.type !== 'account') {
      ctx.status = 403;
      ctx.body = { code: 403, message: '无权访问管理端' };
      return;
    }

    // 设置租户ID
    setCurrentTenantId(payload.tenantId);

    // 将用户信息存储在ctx.state中
    ctx.state.user = payload;
    await next();
  } catch (error) {
    ctx.status = 401;
    ctx.body = { code: 401, message: '无效的认证令牌' };
  }
};

/**
 * 客户端JWT验证中间件
 * 只允许客户端用户访问
 */
export const clientAuthMiddleware = async (ctx: Context, next: Next) => {
  try {
    const token = getTokenFromHeader(ctx);
    if (!token) {
      ctx.status = 401;
      ctx.body = { code: 401, message: '未提供认证令牌' };
      return;
    }

    // 验证令牌
    const payload = verifyToken<ClientJwtPayload>(token);

    // 验证是否为客户端用户
    if (payload.type !== 'client') {
      ctx.status = 403;
      ctx.body = { code: 403, message: '无权访问客户端' };
      return;
    }

    // 设置租户ID
    setCurrentTenantId(payload.tenantId);

    // 将用户信息存储在ctx.state中
    ctx.state.user = payload;
    await next();
  } catch (error) {
    ctx.status = 401;
    ctx.body = { code: 401, message: '无效的认证令牌' };
  }
};

/**
 * 系统管理员验证中间件
 * 只允许系统管理员访问
 */
export const systemAdminMiddleware = async (ctx: Context, next: Next) => {
  try {
    const token = getTokenFromHeader(ctx);
    if (!token) {
      ctx.status = 401;
      ctx.body = { code: 401, message: '未提供认证令牌' };
      return;
    }

    // 验证令牌
    const payload = verifyToken<AccountJwtPayload>(token);

    // 验证是否为主账号且是系统管理员
    if (payload.type !== 'account' || !payload.isSystemAdmin) {
      ctx.status = 403;
      ctx.body = { code: 403, message: '无系统管理员权限' };
      return;
    }

    // 将用户信息存储在ctx.state中
    ctx.state.user = payload;
    await next();
  } catch (error) {
    ctx.status = 401;
    ctx.body = { code: 401, message: '无效的认证令牌' };
  }
};

/**
 * 通用JWT验证中间件
 * 允许任何有效的JWT令牌通过
 */
export const commonAuthMiddleware = async (ctx: Context, next: Next) => {
  try {
    const token = getTokenFromHeader(ctx);
    if (!token) {
      ctx.status = 401;
      ctx.body = { code: 401, message: '未提供认证令牌' };
      return;
    }

    // 验证令牌（可能是管理员账号或客户端用户）
    const payload = verifyToken<AccountJwtPayload | ClientJwtPayload>(token);

    // 设置租户ID
    setCurrentTenantId(payload.tenantId);

    // 将用户信息存储在ctx.state中
    ctx.state.user = payload;
    await next();
  } catch (error) {
    ctx.status = 401;
    ctx.body = { code: 401, message: '无效的认证令牌' };
  }
};

/**
 * 从请求头中获取JWT令牌
 */
const getTokenFromHeader = (ctx: Context): string | null => {
  const authHeader = ctx.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7); // 去掉'Bearer '前缀
};
