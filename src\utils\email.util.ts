import nodemailer from 'nodemailer';
import { SMTP_HOST, SMTP_PORT, SMTP_USER, SMTP_PASS, SMTP_FROM } from '../config/email';

// 创建邮件传输对象
const transporter = nodemailer.createTransport({
  host: SMTP_HOST,
  port: parseInt(SMTP_PORT || '587', 10),
  secure: parseInt(SMTP_PORT || '587', 10) === 465, // true for 465, false for other ports
  auth: {
    user: SMTP_USER,
    pass: SMTP_PASS,
  },
});

/**
 * 发送密码重置邮件
 * @param email 收件人邮箱
 * @param name 收件人姓名
 * @param resetLink 重置链接
 * @returns 是否发送成功
 */
export const sendResetPasswordEmail = async (
  email: string,
  name: string,
  resetLink: string,
): Promise<boolean> => {
  try {
    // 发送邮件
    await transporter.sendMail({
      from: `"SCRM系统" <${SMTP_FROM}>`,
      to: email,
      subject: '密码重置',
      html: `
        <h1>您好，${name}</h1>
        <p>您收到此邮件是因为您（或其他人）请求重置您账号的密码。</p>
        <p>请点击以下链接重置密码：</p>
        <a href="${resetLink}" target="_blank">重置密码</a>
        <p>如果您并未请求此操作，请忽略此邮件，您的密码将保持不变。</p>
        <p>此链接将在1小时后过期。</p>
      `,
    });
    return true;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('发送邮件失败:', error);
    return false;
  }
};

/**
 * 发送欢迎邮件
 * @param email 收件人邮箱
 * @param name 收件人姓名
 * @returns 是否发送成功
 */
export const sendWelcomeEmail = async (email: string, name: string): Promise<boolean> => {
  try {
    // 发送邮件
    await transporter.sendMail({
      from: `"SCRM系统" <${SMTP_FROM}>`,
      to: email,
      subject: '欢迎使用SCRM系统',
      html: `
        <h1>欢迎使用SCRM系统，${name}！</h1>
        <p>感谢您注册成为我们的用户。</p>
        <p>如果您有任何问题，请随时联系我们的支持团队。</p>
      `,
    });
    return true;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('发送邮件失败:', error);
    return false;
  }
};

/**
 * 发送验证邮件
 * @param to 收件人邮箱
 * @param name 用户名
 * @param verificationLink 验证链接
 */
export const sendVerificationEmail = async (
  to: string,
  name: string,
  verificationLink: string,
): Promise<boolean> => {
  const subject = '邮箱验证';
  const html = `
    <h1>邮箱验证</h1>
    <p>您好，${name || to}！</p>
    <p>请点击以下链接验证您的邮箱：</p>
    <a href="${verificationLink}" target="_blank">验证邮箱</a>
    <p>如果不是您本人操作，请忽略此邮件。</p>
  `;

  return sendEmail(to, subject, html);
};

/**
 * 发送邮件
 * @param to 收件人邮箱
 * @param subject 邮件主题
 * @param html 邮件内容（HTML格式）
 */
export const sendEmail = async (to: string, subject: string, html: string): Promise<boolean> => {
  try {
    const mailOptions = {
      from: SMTP_FROM,
      to,
      subject,
      html,
    };

    await transporter.sendMail(mailOptions);
    return true;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('发送邮件失败:', error);
    return false;
  }
};
