import { Context } from 'koa';
import { AccountService } from '../../services/account.service';
import { CaptchaService } from '../../services/captcha.service';
import { hashPassword, comparePassword } from '../../utils/account-auth.util';

export class AdminAuthController {
  private accountService: AccountService;
  private captchaService: CaptchaService;

  constructor() {
    this.accountService = new AccountService();
    this.captchaService = new CaptchaService();
  }

  /**
   * 获取验证码
   */
  getCaptcha = async (ctx: Context): Promise<void> => {
    try {
      const { id, dataUrl } = await this.captchaService.generateCaptcha();
      ctx.body = {
        code: 200,
        data: {
          id,
          dataUrl,
        },
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '获取验证码失败',
      };
    }
  };

  /**
   * 管理员登录
   */
  login = async (ctx: Context): Promise<void> => {
    const { email, password, captchaId, captchaAnswer } = ctx.request.body as {
      email: string;
      password: string;
      captchaId: string;
      captchaAnswer: string;
    };

    try {
      // 验证验证码
      const isCaptchaValid = await this.captchaService.verifyCaptcha(captchaId, captchaAnswer);
      if (!isCaptchaValid) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '验证码错误或已过期',
        };
        return;
      }

      const result = await this.accountService.login({ email, password });

      // 过滤掉敏感信息
      const { account } = result;
      const safeAccount = {
        id: account.id,
        name: account.name,
        email: account.email,
        isSystemAdmin: account.isSystemAdmin,
        tenantId: account.tenantId,
        isActive: account.isActive,
        createdAt: account.createdAt,
        updatedAt: account.updatedAt,
      };

      ctx.body = {
        code: 200,
        data: {
          token: result.token,
          account: safeAccount,
        },
        message: '登录成功',
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error instanceof Error ? error.message : '登录失败',
      };
    }
  };

  /**
   * 获取管理员个人资料
   */
  getProfile = async (ctx: Context): Promise<void> => {
    try {
      const userId = ctx.state.user.id;
      const account = await this.accountService.findOne(userId);

      if (!account) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '账号不存在',
        };
        return;
      }

      // 过滤掉敏感信息
      const safeAccount = {
        id: account.id,
        name: account.name,
        email: account.email,
        phone: account.phone,
        isSystemAdmin: account.isSystemAdmin,
        tenantId: account.tenantId,
        isActive: account.isActive,
        createdAt: account.createdAt,
        updatedAt: account.updatedAt,
      };

      ctx.body = {
        code: 200,
        data: safeAccount,
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '获取个人资料失败',
      };
    }
  };

  /**
   * 更新管理员个人资料
   */
  updateProfile = async (ctx: Context): Promise<void> => {
    try {
      const userId = ctx.state.user.id;
      const { name, phone } = ctx.request.body as {
        name?: string;
        phone?: string;
      };

      const updateData: Record<string, any> = {};
      if (name) updateData.name = name;
      if (phone) updateData.phone = phone;

      if (Object.keys(updateData).length === 0) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '没有提供要更新的数据',
        };
        return;
      }

      const success = await this.accountService.update(userId, updateData);

      if (!success) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '账号不存在',
        };
        return;
      }

      const updatedAccount = await this.accountService.findOne(userId);

      if (!updatedAccount) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '获取更新后的账号信息失败',
        };
        return;
      }

      // 过滤掉敏感信息
      const safeAccount = {
        id: updatedAccount.id,
        name: updatedAccount.name,
        email: updatedAccount.email,
        phone: updatedAccount.phone,
        isSystemAdmin: updatedAccount.isSystemAdmin,
        tenantId: updatedAccount.tenantId,
        isActive: updatedAccount.isActive,
        createdAt: updatedAccount.createdAt,
        updatedAt: updatedAccount.updatedAt,
      };

      ctx.body = {
        code: 200,
        message: '个人资料更新成功',
        data: safeAccount,
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '更新个人资料失败',
      };
    }
  };

  /**
   * 修改密码
   */
  changePassword = async (ctx: Context): Promise<void> => {
    try {
      const userId = ctx.state.user.id;
      const { oldPassword, newPassword } = ctx.request.body as {
        oldPassword: string;
        newPassword: string;
      };

      if (!oldPassword || !newPassword) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '旧密码和新密码是必填项',
        };
        return;
      }

      const account = await this.accountService.findOne(userId);
      if (!account) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '账号不存在',
        };
        return;
      }

      // 验证旧密码
      const isPasswordValid = await comparePassword(oldPassword, account.password);
      if (!isPasswordValid) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '旧密码不正确',
        };
        return;
      }

      // 哈希新密码
      const hashedPassword = await hashPassword(newPassword);

      // 更新密码
      await this.accountService.update(userId, { password: hashedPassword });

      ctx.body = {
        code: 200,
        message: '密码修改成功',
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '修改密码失败',
      };
    }
  };

  /**
   * 忘记密码
   */
  forgotPassword = async (ctx: Context): Promise<void> => {
    const { email } = ctx.request.body as { email: string };

    if (!email) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: '邮箱是必填项',
      };
      return;
    }

    try {
      await this.accountService.forgotPassword(email);

      ctx.body = {
        code: 200,
        message: '重置密码链接已发送至您的邮箱',
        data: true,
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error instanceof Error ? error.message : '发送重置密码邮件失败',
      };
    }
  };

  /**
   * 重置密码
   */
  resetPassword = async (ctx: Context): Promise<void> => {
    const { token, password } = ctx.request.body as {
      token: string;
      password: string;
    };

    if (!token || !password) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: '令牌和新密码是必填项',
      };
      return;
    }

    try {
      const result = await this.accountService.resetPassword(token, password);

      ctx.body = {
        code: 200,
        data: result,
        message: '密码重置成功',
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error instanceof Error ? error.message : '密码重置失败',
      };
    }
  };
}
