// DOM就绪检查
export function domReady(condition: DocumentReadyState[] = ['complete', 'interactive']) {
    return new Promise((resolve) => {
        if (condition.includes(document.readyState)) {
            resolve(true);
        } else {
            document.addEventListener('readystatechange', () => {
                if (condition.includes(document.readyState)) {
                    resolve(true);
                }
            });
        }
    });
}

// 等待元素出现
export function waitForElement(selector, root = document, timeout = 0) {
    return new Promise((resolve, reject) => {
        const element = root.querySelector(selector);
        if (element) {
            resolve(element);
            return;
        }

        const observer = new MutationObserver(() => {
            const element = root.querySelector(selector);
            if (element) {
                resolve(element);
                observer.disconnect();
            }
        });

        observer.observe(root, {
            childList: true,
            subtree: true,
        });

        if (timeout > 0) {
            // 设置超时
            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`等待元素 ${selector} 超时`));
            }, timeout);
        }

    });
}

/**
 * 监听某个localStroage变化
 * @returns
 */
export function onLocalStorageItemChange(key: string, callback: (string) => void) {
    let lastValue = localStorage.getItem(key);
    setInterval(() => {
        const currentValue = localStorage.getItem(key);
        if (lastValue !== currentValue) {
            lastValue = currentValue;
            callback(currentValue)
        }
    }, 1000);
}

export interface TranslateButtonOptions {
    initialText: string;
    loadingText: string;
    errorText: string;
    retryText: string;
    onClick: (button: HTMLElement) => Promise<void>;
}

export function createTranslateButton(options: TranslateButtonOptions): HTMLElement {
    const buttonContainer = document.createElement('div');
    buttonContainer.className = 'translate-button-container';
    buttonContainer.style.cssText = `
        padding: 4px 0;
        display: flex;
        justify-content: center;
        align-items: center;
        color: var(--color-primary);
        cursor: pointer;
        opacity: 0.8;
        transition: opacity 0.2s;
        font-size: 12px;
        user-select: none;
    `;

    buttonContainer.innerHTML = options.initialText;

    buttonContainer.addEventListener('mouseover', () => {
        buttonContainer.style.opacity = '1';
    });
    buttonContainer.addEventListener('mouseout', () => {
        buttonContainer.style.opacity = '0.8';
    });

    buttonContainer.addEventListener('click', async (e) => {
        e.preventDefault();
        e.stopPropagation();

        if (buttonContainer.hasAttribute('is-translating')) return;
        buttonContainer.setAttribute('is-translating', 'true');
        buttonContainer.innerHTML = options.loadingText;
        buttonContainer.style.opacity = '0.6';
        buttonContainer.style.cursor = 'not-allowed';

        try {
            await options.onClick(buttonContainer);
            buttonContainer.innerHTML = options.retryText;
            buttonContainer.style.opacity = '0.8';
            buttonContainer.style.cursor = 'pointer';
            buttonContainer.style.pointerEvents = 'auto';
            buttonContainer.removeAttribute('is-translating');
        } catch (error) {
            buttonContainer.innerHTML = options.errorText;
            buttonContainer.style.color = '#ff4d4f';
            setTimeout(() => {
                buttonContainer.innerHTML = options.initialText;
                buttonContainer.style.color = 'var(--color-primary)';
                buttonContainer.style.opacity = '0.8';
                buttonContainer.style.cursor = 'pointer';
                buttonContainer.removeAttribute('is-translating');
            }, 3000);
        }
    });

    return buttonContainer;
} 