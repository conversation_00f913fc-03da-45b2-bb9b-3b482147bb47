# 会员商品导入脚本使用指南

## 概述

会员商品导入脚本 `scripts/import-membership-products.ts` 用于初始化系统的会员商品数据和权限数据。该脚本会自动创建权限和会员商品，并建立它们之间的关联关系。

## 功能特性

### 🔑 权限管理
- 自动创建系统权限数据
- 支持权限分组管理
- 避免重复创建已存在的权限

### 👑 会员商品管理
- 创建多种会员套餐
- 自动关联权限
- 支持价格、时长、排序等配置

### 🛠️ 操作功能
- **导入数据**：创建权限和会员商品
- **清空数据**：删除所有会员商品
- **查看数据**：显示当前会员商品详情

## 预设数据

### 权限数据
```typescript
const permissionsData = [
  {
    name: '基础翻译',
    key: 'basic.translation',
    description: '基础文本翻译功能',
    group: '翻译功能',
  },
  {
    name: '高级翻译',
    key: 'premium.translation',
    description: '高级翻译功能，支持更多语言和更高精度',
    group: '翻译功能',
  },
  {
    name: '图片翻译',
    key: 'image.translation',
    description: '图片内容识别和翻译功能',
    group: '翻译功能',
  },
  {
    name: '批量翻译',
    key: 'batch.translation',
    description: '批量文本翻译功能',
    group: '翻译功能',
  },
  {
    name: 'API访问',
    key: 'api.access',
    description: '翻译API接口访问权限',
    group: 'API功能',
  },
  {
    name: '无限制使用',
    key: 'unlimited.usage',
    description: '无使用次数限制',
    group: '使用限制',
  },
  {
    name: '优先支持',
    key: 'priority.support',
    description: '优先技术支持服务',
    group: '客户服务',
  },
];
```

### 会员商品数据
```typescript
const membershipProductsData = [
  {
    name: '基础会员',
    description: '适合个人用户的基础翻译服务',
    price: 2900, // 29元
    duration: 30, // 30天
    permissionKeys: ['basic.translation'],
  },
  {
    name: '标准会员',
    description: '包含高级翻译功能，适合专业用户',
    price: 5900, // 59元
    duration: 30,
    permissionKeys: ['basic.translation', 'premium.translation', 'unlimited.usage'],
  },
  {
    name: '高级会员',
    description: '全功能会员，包含图片翻译和批量翻译',
    price: 9900, // 99元
    duration: 30,
    permissionKeys: [
      'basic.translation',
      'premium.translation',
      'image.translation',
      'batch.translation',
      'unlimited.usage',
    ],
  },
  {
    name: '专业会员',
    description: '专业用户专享，包含API访问权限',
    price: 19900, // 199元
    duration: 30,
    permissionKeys: [
      'basic.translation',
      'premium.translation',
      'image.translation',
      'batch.translation',
      'api.access',
      'unlimited.usage',
      'priority.support',
    ],
  },
  {
    name: '年度会员',
    description: '年度套餐，享受最大优惠和全部功能',
    price: 99900, // 999元
    duration: 365,
    permissionKeys: [
      'basic.translation',
      'premium.translation',
      'image.translation',
      'batch.translation',
      'api.access',
      'unlimited.usage',
      'priority.support',
    ],
  },
];
```

## 使用方法

### 1. 导入会员商品数据
```bash
npm run import-membership-products
```

### 2. 清空会员商品数据
```bash
npm run import-membership-products clear
```

### 3. 查看当前会员商品
```bash
npm run import-membership-products show
```

## 脚本配置

### package.json 配置
需要在服务端项目的 `package.json` 中添加以下脚本：

```json
{
  "scripts": {
    "import-membership-products": "ts-node scripts/import-membership-products.ts"
  }
}
```

### 环境要求
- Node.js 环境
- TypeScript 支持
- 数据库连接配置正确

## 执行流程

### 导入流程
1. **连接数据库**：初始化数据库连接
2. **检查现有数据**：避免重复导入
3. **导入权限**：创建或更新权限数据
4. **导入会员商品**：创建会员商品并关联权限
5. **显示结果**：输出导入结果统计

### 清空流程
1. **连接数据库**
2. **查找现有商品**
3. **逐个删除商品**（会自动删除关联的权限关系）
4. **显示结果**

### 查看流程
1. **连接数据库**
2. **获取商品列表**
3. **获取详细信息**（包含权限信息）
4. **格式化显示**

## 注意事项

### 🔒 数据安全
- 脚本会检查现有数据，避免重复导入
- 清空操作不可逆，请谨慎使用
- 建议在测试环境先验证

### 🔧 自定义配置
- 可以修改 `permissionsData` 和 `membershipProductsData` 来自定义数据
- 价格单位为分（1元 = 100分）
- 时长单位为天

### 📊 权限关联
- 会员商品通过 `permissionKeys` 关联权限
- 权限必须先存在才能关联
- 支持一个商品关联多个权限

## 错误处理

### 常见错误
1. **数据库连接失败**：检查数据库配置和网络连接
2. **权限不存在**：确保权限数据正确且已创建
3. **重复导入**：脚本会自动检测并提示

### 调试建议
1. 使用 `show` 命令查看当前数据状态
2. 检查控制台输出的详细错误信息
3. 确认数据库表结构正确

## 扩展说明

### 添加新权限
1. 在 `permissionsData` 中添加新权限定义
2. 在相应的会员商品中添加权限关联
3. 重新运行导入脚本

### 添加新会员商品
1. 在 `membershipProductsData` 中添加商品定义
2. 指定相应的权限关联
3. 设置合适的价格和时长

### 修改现有数据
1. 建议先清空现有数据
2. 修改脚本中的数据定义
3. 重新导入数据

## 相关文件

- `scripts/import-membership-products.ts` - 导入脚本
- `src/entities/MembershipProduct.ts` - 会员商品实体
- `src/entities/Permission.ts` - 权限实体
- `src/entities/MembershipPermission.ts` - 关联实体
- `src/services/membership-product.service.ts` - 会员商品服务
- `src/services/permission.service.ts` - 权限服务

## 总结

会员商品导入脚本提供了完整的会员系统初始化功能，支持权限管理、商品管理和数据维护。通过简单的命令即可完成复杂的数据初始化工作，为会员系统的快速部署提供了便利。
