# 积分商品功能文档

## 功能概述

积分商品功能为SCRM系统提供了完整的积分充值和消费体系。用户可以通过购买积分商品获得积分，使用翻译功能时会消耗相应积分。

## 核心特性

### 1. 积分商品管理
- 支持多种积分商品规格
- 可设置折扣率和赠送积分
- 商品状态管理（启用/禁用）

### 2. 积分消费机制
- 按翻译供应商和类型设置不同消费标准
- 翻译前预扣积分，失败时自动退还
- 完整的积分流水记录

### 3. 支付集成
- 集成zpay支付平台
- 支持支付宝、微信支付等多种方式
- 异步回调处理支付结果

### 4. 订单管理
- 完整的订单生命周期管理
- 订单过期自动处理
- 支付状态实时更新

## 数据库设计

### 积分商品表 (points_products)
```sql
CREATE TABLE points_products (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(100) NOT NULL COMMENT '商品名称',
  description TEXT COMMENT '商品描述',
  points INT NOT NULL COMMENT '积分数量',
  price INT NOT NULL COMMENT '价格（分）',
  discount_rate DECIMAL(3,2) DEFAULT 1.00 COMMENT '折扣率',
  bonus_points INT DEFAULT 0 COMMENT '赠送积分',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  sort INT DEFAULT 0 COMMENT '排序权重',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 积分订单表 (points_orders)
```sql
CREATE TABLE points_orders (
  id VARCHAR(36) PRIMARY KEY,
  order_no VARCHAR(32) UNIQUE NOT NULL COMMENT '订单号',
  client_id VARCHAR(36) NOT NULL COMMENT '用户ID',
  product_id VARCHAR(36) NOT NULL COMMENT '商品ID',
  original_price INT NOT NULL COMMENT '原价（分）',
  discount_price INT NOT NULL COMMENT '折扣价（分）',
  actual_price INT NOT NULL COMMENT '实际支付价格（分）',
  points INT NOT NULL COMMENT '获得积分',
  bonus_points INT DEFAULT 0 COMMENT '赠送积分',
  status ENUM('pending','paid','cancelled','expired') DEFAULT 'pending',
  payment_method VARCHAR(50) COMMENT '支付方式',
  transaction_id VARCHAR(100) COMMENT '交易ID',
  paid_at DATETIME COMMENT '支付时间',
  expired_at DATETIME COMMENT '过期时间',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 积分流水表 (points_transactions)
```sql
CREATE TABLE points_transactions (
  id VARCHAR(36) PRIMARY KEY,
  client_id VARCHAR(36) NOT NULL COMMENT '用户ID',
  type ENUM('recharge','consume','refund','bonus','admin_adjust') NOT NULL,
  amount INT NOT NULL COMMENT '积分数量（正负）',
  balance INT NOT NULL COMMENT '交易后余额',
  description VARCHAR(200) NOT NULL COMMENT '描述',
  reference_type ENUM('order','translation','admin') COMMENT '关联类型',
  reference_id VARCHAR(100) COMMENT '关联ID',
  metadata JSON COMMENT '扩展数据',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## API接口

### 客户端接口

#### 1. 获取积分商品列表
```
GET /api/client/points/products
```

#### 2. 获取用户积分余额
```
GET /api/client/points/balance
```

#### 3. 获取积分流水记录
```
GET /api/client/points/transactions?page=1&limit=20
```

#### 4. 创建积分订单
```
POST /api/client/points/orders
{
  "productId": "product-uuid"
}
```

#### 5. 创建支付链接
```
POST /api/client/points/payment
{
  "orderNo": "PO1234567890123",
  "paymentMethod": "alipay"
}
```

#### 6. 获取用户订单列表
```
GET /api/client/points/orders?page=1&limit=20
```

## 积分消费配置

在 `src/config/points-consumption.ts` 中配置不同翻译供应商和类型的积分消费：

```typescript
export const POINTS_CONSUMPTION = {
  baidu: {
    text: 1,      // 文本翻译每次消耗1积分
    image: 5,     // 图片翻译每次消耗5积分
    document: 10  // 文档翻译每次消耗10积分
  },
  youdao: {
    text: 1,
    image: 5,
    document: 10
  }
};
```

## 环境变量配置

在 `.env` 文件中添加zpay配置：

```env
# ZPay Configuration
ZPAY_PID=your_zpay_pid
ZPAY_KEY=your_zpay_key
ZPAY_BASE_URL=https://z-pay.cn
ZPAY_NOTIFY_URL=http://your-domain.com/api/client/points/payment/notify
ZPAY_RETURN_URL=http://your-domain.com/payment/success
ZPAY_SITENAME=SCRM系统
```

## 部署和初始化

### 1. 导入积分商品数据
```bash
npm run import-points-products
```

### 2. 清空积分商品数据（如需重新导入）
```bash
npm run import-points-products clear
```

## 使用流程

### 用户充值流程
1. 用户查看积分商品列表
2. 选择商品创建订单
3. 获取支付链接进行支付
4. 支付成功后自动增加积分
5. 记录积分流水

### 翻译消费流程
1. 用户发起翻译请求
2. 系统根据供应商和类型扣除积分
3. 调用翻译API
4. 翻译成功返回结果
5. 翻译失败自动退还积分

## 测试

运行积分功能测试：
```bash
npm test src/tests/points.service.test.ts
npm test src/tests/points.integration.test.ts
```

## 注意事项

1. **积分扣除时机**：翻译前预扣积分，避免恶意调用
2. **失败处理**：翻译失败时自动退还积分，确保用户权益
3. **订单过期**：订单30分钟后自动过期，避免长期占用
4. **支付安全**：使用MD5签名验证支付回调的真实性
5. **流水记录**：所有积分变动都有详细的流水记录

## 扩展功能

### 1. 积分有效期
可以为积分设置有效期，过期自动清零

### 2. 积分等级
根据用户积分总量设置不同等级，享受不同优惠

### 3. 积分兑换
支持积分兑换其他商品或服务

### 4. 推荐奖励
用户推荐新用户注册可获得积分奖励
