import { Repository, <PERSON><PERSON>han } from 'typeorm';
import { Account } from '../entities/Account';
import { BaseService } from './base.service';
import { AppDataSource } from '../config/database';
import { hashPassword, comparePassword, generateAccountToken } from '../utils/account-auth.util';
import { AccountLoginDto, ClientRegisterDto } from '../types/auth.interface';
import crypto from 'crypto';
import { sendResetPasswordEmail } from '../utils/email.util';
import { getCurrentTenantId } from '../utils/tenant.util';
import { ADMIN_URL } from '../config/app';

export class AccountService extends BaseService<Account> {
  private accountRepository: Repository<Account>;

  constructor() {
    const accountRepository = AppDataSource.getRepository(Account);
    super(accountRepository);
    this.accountRepository = accountRepository;
  }

  /**
   * 注册主账号
   */
  async register(registerDto: ClientRegisterDto): Promise<{ account: Account; token: string }> {
    const { email, password, name, phone } = registerDto;

    // 检查邮箱是否已注册
    const existingAccount = await this.accountRepository.findOne({ where: { email } });
    if (existingAccount) {
      throw new Error('邮箱已被注册');
    }

    // 哈希密码并创建用户
    const hashedPassword = await hashPassword(password);

    // 使用当前上下文中的租户ID
    const tenantId = getCurrentTenantId();

    const account = await this.create({
      email,
      password: hashedPassword,
      name,
      phone,
      isSystemAdmin: false,
      tenantId,
    });

    // 生成JWT令牌
    const token = generateAccountToken(account);

    return { account, token };
  }

  /**
   * 主账号登录
   */
  async login(loginDto: AccountLoginDto): Promise<{ account: Account; token: string }> {
    const { email, password } = loginDto;

    // 查找用户
    const account = await this.accountRepository.findOne({ where: { email } });
    if (!account) {
      throw new Error('账号不存在');
    }

    // 验证密码
    const isPasswordValid = await comparePassword(password, account.password);
    if (!isPasswordValid) {
      throw new Error('密码错误');
    }

    // 检查账号是否被禁用
    if (!account.isActive) {
      throw new Error('该账号已被禁用');
    }

    // 生成JWT令牌
    const token = generateAccountToken(account);

    return { account, token };
  }

  /**
   * 主账号忘记密码
   */
  async forgotPassword(email: string): Promise<boolean> {
    // 查找用户
    const account = await this.accountRepository.findOne({ where: { email } });
    if (!account) {
      throw new Error('账号不存在');
    }

    // 生成重置令牌和到期时间
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetPasswordToken = crypto.createHash('sha256').update(resetToken).digest('hex');
    const resetPasswordExpires = new Date(Date.now() + 3600000); // 1小时后过期

    // 更新用户的重置令牌和到期时间
    await this.update(account.id, {
      resetPasswordToken,
      resetPasswordExpires,
    });

    // 生成重置链接
    const resetLink = `${ADMIN_URL || 'http://localhost:3000'}/reset-password?token=${resetToken}`;

    // 发送重置密码邮件
    return sendResetPasswordEmail(email, account.name, resetLink);
  }

  /**
   * 重置主账号密码
   */
  async resetPassword(token: string, newPassword: string): Promise<boolean> {
    // 哈希令牌
    const resetPasswordToken = crypto.createHash('sha256').update(token).digest('hex');

    // 查找具有该令牌且令牌未过期的账号
    const account = await this.accountRepository.findOne({
      where: {
        resetPasswordToken,
        resetPasswordExpires: MoreThan(new Date()),
      },
    });

    if (!account) {
      throw new Error('无效或过期的重置令牌');
    }

    // 哈希新密码并更新账号
    const hashedPassword = await hashPassword(newPassword);
    await this.update(account.id, {
      password: hashedPassword,
      resetPasswordToken: '',
      resetPasswordExpires: undefined,
    });

    return true;
  }
}
