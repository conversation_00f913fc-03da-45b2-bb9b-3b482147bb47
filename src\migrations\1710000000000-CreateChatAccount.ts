import { MigrationInterface, QueryRunner, Table, TableForeignKey, TableIndex } from 'typeorm';

export class CreateChatAccount1710000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'chat_account',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            length: '36',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'uuid',
          },
          {
            name: 'account_id',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'client_id',
            type: 'varchar',
            length: '36',
          },
          {
            name: 'nickname',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'avatar',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // 添加外键
    await queryRunner.createForeignKey(
      'chat_account',
      new TableForeignKey({
        columnNames: ['client_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'client',
        onDelete: 'CASCADE',
      }),
    );

    // 添加索引
    await queryRunner.createIndex(
      'chat_account',
      new TableIndex({
        name: 'IDX_CHAT_ACCOUNT_ID',
        columnNames: ['account_id'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable('chat_account');
    if (table) {
      const foreignKey = table.foreignKeys.find((fk) => fk.columnNames.indexOf('client_id') !== -1);
      if (foreignKey) {
        await queryRunner.dropForeignKey('chat_account', foreignKey);
      }
      await queryRunner.dropIndex('chat_account', 'IDX_CHAT_ACCOUNT_ID');
      await queryRunner.dropTable('chat_account');
    }
  }
}
